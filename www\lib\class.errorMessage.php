<?php
//ErrorMessage

class ErrorMessage {
  
  const ERRMESS_HEAD = '<div class="col-md-12 text-center alert alert-danger">';
  const ERRMESS_FOOT = '</div>';
  
  private $msg;
  private $existerror = false;
  private $finalmessage;
  
  function __construct() {
   
  }
  
  function addErrorMessage($mess) {
   $this->msg .= ''.$mess.'<br />';
   if (!$this->existerror) {
    $this->existerror = true;
   }
  }
  
  function getErrorMessage() {
   $this->finalMessage = self::ERRMESS_HEAD.$this->msg.self::ERRMESS_FOOT;
   return $this->finalMessage;
  }
  
  function getExistError() {
   return $this->existerror;
  }
    
}

?>
