<?php
 class NavPtgNaplo {
  
  /*
   Properties
  */
  private $rId = 0;
  
  public $navFelhasznalo = "caslh9mjixps7bl";
  public $navSigkey = "2c-8a1f-7697dc19ff8b492KV93VIA0I";
  public $navPassword = 'Klfg_987fn_4567?Rf9cv';
  public $navPwhash = '';
  public $navChangekey = "247b492KV93VK89I";
  public $navAdoszam = "20392886";

  private $navSoftwareId = "HU20392886ARMATAX1";
  private $navSoftwareName = "ArmaTax";
  private $navSoftwareOperation = "LOCAL_SOFTWARE";
  private $navSoftwareMainVersion = "2.0";
  private $navSoftwareDevName = "Armamenta Bt.";
  private $navSoftwareDevContact = "<EMAIL>";
  private $navSoftwareDevCountryCode = "HU";
  private $navSoftwareDevTaxNumber = "20392886-2-16";

  private $replaceables = array("env:", "ns3:", "ns2:", ":env");

  private $url_status = 'https://api-onlinepenztargep.nav.gov.hu/queryCashRegisterFile/v1/queryCashRegisterStatus';
  private $url = 'https://api-onlinepenztargep.nav.gov.hu/queryCashRegisterFile/v1/queryCashRegisterFile';
  private $queryAp = '';
  private $lastNaploNum = 0;
  private $minNaploNum = 0;
  private $naploArray = array();
  
  /*
  Constructor
  */
  function __construct($ap = 'A15905577') {
    $this->queryAp = $ap;
    $this->init();
  }
  
  /*
  Init
  */
  
  public function init() {
    $this->navPwhash = strtoupper(hash("sha512", $this->navPassword));
    $this->generateRID();
  }

  private function generateRID() {
    $id = "RID" . microtime() . mt_rand(10000, 99999);
    $id = preg_replace("/[^A-Z0-9]/", "", $id);
    $id = substr($id, 0, 30);
    $this->rId = $id;
  }

  private function generateRIDSignature() {
    $rq = $this->rId;
    $rq .= gmdate("YmdHis");
    $rq .= $this->navSigkey;
    $rqsid = strtoupper(hash("sha3-512", $rq));
    return $rqsid;
  }

  private function generateRequestUserData() {
    $xml = '<com:user>
    <com:login>'.$this->navFelhasznalo.'</com:login>
    <com:passwordHash cryptoType="SHA-512">'.$this->navPwhash.'</com:passwordHash>
    <com:taxNumber>'.$this->navAdoszam.'</com:taxNumber>
    <com:requestSignature cryptoType="SHA3-512">'.$this->generateRIDSignature().'</com:requestSignature>
    <!--<signKey></signKey>-->
    </com:user>';
    return $xml;
  }

  private function generateRequestSoftwareData() {
    $xml = '<api:software>
    <api:softwareId>'.$this->navSoftwareId.'</api:softwareId>
    <api:softwareName>'.$this->navSoftwareName.'</api:softwareName>
    <api:softwareOperation>'.$this->navSoftwareOperation.'</api:softwareOperation>
    <api:softwareMainVersion>'.$this->navSoftwareMainVersion.'</api:softwareMainVersion>
    <api:softwareDevName>'.$this->navSoftwareDevName.'</api:softwareDevName>
    <api:softwareDevContact>'.$this->navSoftwareDevContact.'</api:softwareDevContact>
    <api:softwareDevCountryCode>'.$this->navSoftwareDevCountryCode.'</api:softwareDevCountryCode>
    <api:softwareDevTaxNumber>'.$this->navSoftwareDevTaxNumber.'</api:softwareDevTaxNumber>
    </api:software>';
    return $xml;
  }

  private function generateStatusXmlRq() {
    $this->generateRID();
    $xml = '<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:api="http://schemas.nav.gov.hu/OPF/1.0/api"
    xmlns:com="http://schemas.nav.gov.hu/NTCA/1.0/common">
    <soap:Header/>
    <soap:Body>
    <api:QueryCashRegisterStatusRequest>
    <com:header>
    <com:requestId>'.$this->rId.'</com:requestId>
    <com:timestamp>'.gmdate("Y-m-d").'T'.gmdate("H:i:s").'Z</com:timestamp>
    <com:requestVersion>1.0</com:requestVersion>
    <com:headerVersion>1.0</com:headerVersion>
    </com:header>
    '.$this->generateRequestUserData().'
    '.$this->generateRequestSoftwareData().'
    <api:cashRegisterStatusQuery>
    <api:APNumberList>
    <api:APNumber>'.$this->queryAp.'</api:APNumber>
    </api:APNumberList>
    </api:cashRegisterStatusQuery>
    </api:QueryCashRegisterStatusRequest>
    </soap:Body>
    </soap:Envelope>';
    return $xml;
  }

  private function generateFileXmlRq() {
    $this->generateRID();
    $xml = '<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:api="http://schemas.nav.gov.hu/OPF/1.0/api" xmlns:com="http://schemas.nav.gov.hu/NTCA/1.0/common">
    <soap:Header/>
    <soap:Body>
    <api:QueryCashRegisterFileDataRequest>
    <com:header>
    <com:requestId>'.$this->rId.'</com:requestId>
    <com:timestamp>'.gmdate("Y-m-d").'T'.gmdate("H:i:s").'Z</com:timestamp>
    <com:requestVersion>1.0</com:requestVersion>
    <com:headerVersion>1.0</com:headerVersion>
    </com:header>
    '.$this->generateRequestUserData().'
    '.$this->generateRequestSoftwareData().'
    <api:cashRegisterFileDataQuery>
    <api:APNumber>'.$this->queryAp.'</api:APNumber>
    <api:fileNumberStart>'.$this->lastNaploNum.'</api:fileNumberStart>
    <api:fileNumberEnd>'.$this->lastNaploNum.'</api:fileNumberEnd>
    </api:cashRegisterFileDataQuery>
    </api:QueryCashRegisterFileDataRequest>
    </soap:Body>
    </soap:Envelope>';
    return $xml;
  }

  private function generateFileXmlRqByParam($num) {
    $this->generateRID();
    $xml = '<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:api="http://schemas.nav.gov.hu/OPF/1.0/api" xmlns:com="http://schemas.nav.gov.hu/NTCA/1.0/common">
    <soap:Header/>
    <soap:Body>
    <api:QueryCashRegisterFileDataRequest>
    <com:header>
    <com:requestId>'.$this->rId.'</com:requestId>
    <com:timestamp>'.gmdate("Y-m-d").'T'.gmdate("H:i:s").'Z</com:timestamp>
    <com:requestVersion>1.0</com:requestVersion>
    <com:headerVersion>1.0</com:headerVersion>
    </com:header>
    '.$this->generateRequestUserData().'
    '.$this->generateRequestSoftwareData().'
    <api:cashRegisterFileDataQuery>
    <api:APNumber>'.$this->queryAp.'</api:APNumber>
    <api:fileNumberStart>'.$num.'</api:fileNumberStart>
    <api:fileNumberEnd>'.$num.'</api:fileNumberEnd>
    </api:cashRegisterFileDataQuery>
    </api:QueryCashRegisterFileDataRequest>
    </soap:Body>
    </soap:Envelope>';
    return $xml;
  }

  private function getPTGStatusNaplofilenum() {
    $curl_s = curl_init();
    curl_setopt_array($curl_s, array(
      CURLOPT_URL => $this->url_status,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => 'utf-8',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => 'POST',
      CURLOPT_POSTFIELDS => $this->generateStatusXmlRq(),
      CURLOPT_HTTPHEADER => array("content-type: application/soap+xml"),
      ),
    );

    $response_s = curl_exec($curl_s);
    //print $response_s;
    curl_close($curl_s);
    $pos1 = strpos($response_s, '<');
    $pos2 = strrpos($response_s, '>');
    $pos3 = $pos2-$pos1+1;
    $dt = substr($response_s, $pos1, $pos3);
    $dt = str_replace($this->replaceables, '', $dt);
    $status_response = json_decode(json_encode(simplexml_load_string($dt)));
    $this->lastNaploNum = $status_response->Body->QueryCashRegisterStatusResponse->cashRegisterStatusResult->cashRegisterStatusList->cashRegisterStatus->maxAvailableFileNumber;
    $this->minNaploNum = $status_response->Body->QueryCashRegisterStatusResponse->cashRegisterStatusResult->cashRegisterStatusList->cashRegisterStatus->minAvailableFileNumber;
  }

  public function getPTGStatusNaploFile() {
    $this->getPTGStatusNaplofilenum();
    //print $this->generateFileXmlRq();
    $curl = curl_init();
    curl_setopt_array($curl, array(
      CURLOPT_URL => $this->url,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_ENCODING => 'utf-8',
      CURLOPT_MAXREDIRS => 10,
      CURLOPT_TIMEOUT => 0,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
      CURLOPT_CUSTOMREQUEST => 'POST',
      CURLOPT_POSTFIELDS => $this->generateFileXmlRq(),
      CURLOPT_HTTPHEADER => array("content-type: application/soap+xml"),
      ),
    );

    $response = curl_exec($curl);
    //print $response;
    curl_close($curl);
    $rpos1 = strpos($response, 'PK');
    $rpos2 = strrpos($response, '------=_Part');
    $len = strlen('------=_Part');
    $rpos3 = $rpos2-$rpos1;
    $p7bd = substr($response, $rpos1, $rpos3);
    $tempZipFile = '/var/www/zip/arc.zip';
    file_put_contents($tempZipFile, $p7bd);
    $zip = new ZipArchive();
    if ($zip->open($tempZipFile) === true) {
      $targetDir = '/var/www/zip/';
      $zip->extractTo($targetDir);
      $zippedfile = $zip->getNameIndex(0);
      $zip->close();
      return '/var/www/zip/'.$zippedfile;
    } else {
      echo 'hiba a fájl kitörömítésekor!';
    }
  }

  public function getPTGStatusNaploFileByNum() {
    $this->getPTGStatusNaplofilenum();
    //print $this->generateFileXmlRq();
    for ($i=$this->minNaploNum; $i <= $this->lastNaploNum ; $i++) {  
      $curl = curl_init();
      curl_setopt_array($curl, array(
        CURLOPT_URL => $this->url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => 'utf-8',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => $this->generateFileXmlRqByParam($i),
        CURLOPT_HTTPHEADER => array("content-type: application/soap+xml"),
        ),
      );

      $response = curl_exec($curl);
      //print $response;
      curl_close($curl);
      $rpos1 = strpos($response, 'PK');
      $rpos2 = strrpos($response, '------=_Part');
      $len = strlen('------=_Part');
      $rpos3 = $rpos2-$rpos1;
      $p7bd = substr($response, $rpos1, $rpos3);
      $tempZipFile = '/var/www/zip/arc.zip';
      file_put_contents($tempZipFile, $p7bd);
      $zip = new ZipArchive();
      if ($zip->open($tempZipFile) === true) {
        $targetDir = '/var/www/zip/';
        $zip->extractTo($targetDir);
        $this->naploArray [] = $targetDir.$zip->getNameIndex(0);
        $zip->close();
      }
    }
  }

  public function decodeP7bFile() {
    $this->getPTGStatusNaploFile();
    $data = file_get_contents($fl);
    //print $data;
    $pos1 = strpos($data, '<?');
    $pos2 = strrpos($data, '</ROWS>');
    $len = strlen($data);
    $pos3 = $pos2-$pos1+7;
    $dt = substr($data, $pos1, $pos3);
    $xml = new SimpleXMLElement($dt);
    print $xml->INF->DTS.'<br />';
    print '<PRE>';
    var_dump($xml);    
  }

  public function decodeP7bFileFromArray() {
    $this->getPTGStatusNaploFileByNum();
    //$responsearray = array("A" => 0, "B" => 0, "C" => 0, "D" => 0, "E" => 0);
    $responsearray = array();
    //var_dump ($this->naploArray);
    foreach ($this->naploArray as $key => $value) {
      $data = file_get_contents($value);
      //print $data;
      $pos1 = strpos($data, '<?');
      $pos2 = strrpos($data, '</ROWS>');
      $len = strlen($data);
      $pos3 = $pos2-$pos1+7;
      $dt = substr($data, $pos1, $pos3);
      $xml = new SimpleXMLElement($dt);
      if ($xml->NFN->ZSZ != '') {
        //Több gyűjtő esetén más a módszer. GYJ-ben van minden GYF és GYE
        //print substr($xml->INF->DTS, 0, 10).'<br />';
        //print $xml->NFN->ZSZ.'<br />';
        $zarszam = trim($xml->NFN->ZSZ).'|'.substr($xml->INF->DTS, 0, 10);
        //print_r $zarszam.'<br />';
        $responsearray [$zarszam] = array("A" => 0, "B" => 0, "C" => 0, "D" => 0, "E" => 0, 'GT' => 0);
        /*print $xml->NFN->GYF->GYJ.': ';
        print $xml->NFN->GYF->GYE.'Ft<br />';*/
        $penz = array();
        foreach ($xml->NFN->GYF->GYE as $key1 => $value1) {
          $penz [] = $value1;
        }
        $gyujto = array();
        foreach ($xml->NFN->GYF->GYJ as $key => $value) {
          $gyujto [] = $value;
        }
        foreach ($gyujto as $key => $value) {
          //print $value.':'.$penz[$key].'<br />';
          $afakod = substr($value, 0, 1);
          $responsearray [$zarszam] [$afakod] += $penz[$key];
        }
        $responsearray [$zarszam] ['GT'] = $xml->NFN->NSG;
      }
      //print '<hr />';
    }
    return $responsearray;
  }

  /*Setterek*/
  public function setNavFelhasznalo($adat) {
    $this->navFelhasznalo = $adat;
  }
  
  public function setNavSigkey($adat) {
    $this->navSigkey = $adat;
  }

  public function setNavPassword($adat) {
    $this->navPassword = $adat;
    $this->navPwhash = strtoupper(hash("sha512", $this->navPassword));
  }
  
  public function setNavChangekey($adat) {
    $this->navChangekey = $adat;
  }
  
  public function setNavAdoszam($adat) {
    $this->navAdoszam = $adat;
  }

  public function setNavSoftwareId($adat) {
    $this->navSoftwareId = $adat;
  }

  public function setNavSoftwareName($adat) {
    $this->navSoftwareName = $adat;
  }
  
  public function setNavSoftwareOperation($adat) {
    $this->navSoftwareOperation = $adat;
  }
  
  public function setNavSoftwareMainVersion($adat) {
    $this->navSoftwareMainVersion = $adat;
  }
  
  public function setNavSoftwareDevName($adat) {
    $this->navSoftwareDevName = $adat;
  }
  
  public function setNavSoftwareDevContact($adat) {
    $this->navSoftwareDevContact = $adat;
  }
  
  public function setNavSoftwareDevCountryCode($adat) {
    $this->navSoftwareDevCountryCode = $adat;
  }
  
  public function setNavSoftwareDevTaxNumber($adat) {
    $this->navSoftwareDevTaxNumber = $adat;
  }
  
 }
?>
