/**
* Backyard
* Landing Page Template
* Author: MoxDesign - http://www.moxdesign.com
**/

html, body {
	width: 100%;
	overflow-x: hidden;
}
body {
	height:100%;
	font-family: "Lato", "Serif";
	font-weight: 300;
	padding:0;
	margin:0;
	font-size:16px;
	line-height: 28px;
	color: #777; 
	background: #fff;
	overflow-x:hidden;
	position: relative;
}
h1 { font-size: 3em; line-height:1.2em; margin:0 0 .8em; }
h2 { font-size: 1.8em; line-height:1.4em; margin:0 0 .8em;  }
h3 { font-size: 1.4em; line-height:1.4em; }
h4 { font-size: 1.25em; line-height:1.4em;}
h5 { font-size: 1.1em; line-height:1.4em; }
h6 { font-size: 1em; line-height:1.2em; }

h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
	text-decoration: none
}	 
p { 
	padding:0 0 1em;
	margin:0;
}
a {
	color: #3eb0f7;
	outline: 0;
	font-weight: bold;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
} 
a:hover {
	text-decoration: none;
	color: #1f2222;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
.highlight {
	color: #3eb0f7;
}
#preloader { 
	position: fixed; 
	left: 0; 
	top: 0; 
	z-index: 999; 
	width: 100%; 
	height: 100%; 
	overflow: visible; 
	background: #fff url(../uploads/loading.gif) no-repeat center center;
}
.fullscreen {
    width:100%;
    min-height:100%;
	background-repeat:no-repeat;
    background-position:50% 50%;
    background-position:50% 50%\9 !important;
}
.overlay {
    background-color: rgba(0, 0, 0, 0.5);
    position: relative;
    width: 100%;
    height: 100%;
    display: block;
}
#menu {
	z-index: 999;
}
.navbar-default {
	background: rgba(255, 255, 255, 0.95);
	box-shadow: 0px 0.5px 2px #cecece;
}
.navbar-default .navbar-brand {
    padding: 7px;
}
.navbar-default .navbar-nav > li > a {
    color: #777;
	font-weight: 500;
}
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
    color: #3eb0f7;
}
.navbar-default .navbar-nav > .active > a, 
.navbar-default .navbar-nav > .active > a:hover, 
.navbar-default .navbar-nav > .active > a:focus {
	background: transparent;
    color: #3eb0f7;
}
.site-name img{
	width: 150px; 
	height: 37px
}
.logo {
	margin: 100px 0 40px 0;
}
.logo img{
	width: 150px; 
	height: 37px
}
.landing h1{
	font-size: 56px;
	font-weight: 300;
	color: #fff;
	margin: 30px 0 30px 0;
	text-shadow: 0 1px 2px rgba(0, 0, 0, .6);
}
.landing p, .landing h2 {
	color: #fff;
}
.landing-text {
	margin-bottom: 20px;
}
.landing-text p {
	font-size: 17px !important; 
}
.action-glass {
	font-size: 17px;
	margin: 30px 0 10px;
	margin-right: 10px;
	line-height: 20px;
	padding: 15px 35px;
	height: 50px;
	border: 2px solid #3eb0f7;
	background: transparent;
	transition: all 0.4s;
	color: #3eb0f7;
	border-radius: 100px;
}
.action-glass:hover {
	background: #3eb0f7;
	color: white;
}
.action-blue {
	font-size: 17px;
	margin: 30px 0 10px;
	margin-right: 10px;
	line-height: 20px;
	padding: 15px 35px;
	height: 50px;
	border: 2px solid #3eb0f7;
	background: #3eb0f7;
	transition: all 0.4s;
	color: white;
	border-radius: 100px;
}
.action-blue:hover {
	background: transparent;
	color: #3eb0f7;
}
.head-btn {
	margin-bottom: 100px;
}
.option {
	text-transform: uppercase;
	padding: 5px;
	min-width: 80px;
	margin-right: 5px;
	transition: all 0.4s;
	font-size: 14px;
	color: #fff;
}
.option .fa {
	font-size: 16px;
	margin-right: 10px;
}
.option:hover {
	color: #3eb1f8;
}

.signup-header {
	margin: 100px 0 100px;
	background: rgba(255,255,255,0.2);
	border-radius: 4px;
	padding-left: 20px;
	padding-right: 20px;	
}
.signup-header h3{
	padding: 20px 0 10px;
	color: white;
	font-weight: 300;
}
.form-header input {
	position: relative;
	padding: 5px 15px;
}
.form-header .form-control {
	border-radius: 0;
	border: solid 1px #dadada;
	background-color: #fff;
	color: #333;
	height: 55px;
}
.form-header .btn {
	border-radius: 0;
	height: 55px;
	width: 100%;
	background-color: #3eb0f7;
	color: white;
	font-size: 17px !important;
	padding: 0 33px;
	border: none;
	margin: 0;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
.form-header .btn:hover {
	background-color: #1f96e0;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
.signup-registration {
	margin: 20px 0 50px;
}
.form-registration { 
	background: rgba(255,255,255,0.2); 
	padding: 20px 20px; 
	margin-top: 50px; 
}
.form-registration .form-group { 
	margin-bottom: 45px;
	margin-top: 20px; 
}
.form-registration input {
	margin-bottom: 20px;
	position: relative;
	padding: 5px 15px;
}
.form-registration label {
	color: white;
}
.form-registration .form-control {
	border-radius: 0;
	border: solid 1px #dadada;
	background-color: #fff;
	color: #333;
	height: 55px;
}
.form-registration .btn {
	border-radius: 0;
	height: 55px;
	width: 100%;
	background-color: #3eb0f7;
	color: white;
	font-size: 17px !important;
	padding: 0 33px;
	border: none;
	margin: 0;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
.form-registration .btn:hover {
	background-color: #1f96e0;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
.privacy {
	padding-top: 0;
	font-size: 13px;
}
.signup-header p {
	color: white;
}
.privacy a {
	text-decoration: underline;
	color: white;
	font-weight: 300;
}
#intro {
	padding: 100px 0 70px;
}
#intro h2 {
	font-size: 40px;
	font-weight: 300;
	margin: 15px 0 15px 0;
}
.intro-pic {
	margin-top: 20px;
}
.btn-action {
	font-size: 17px;
	margin: 30px 0 10px;
	line-height: 20px;
	padding: 15px 35px;
	height: 50px;
	border: solid 2px #3eb0f7;
	background: white;
	transition: all 0.4s;
	color: #3eb0f7;
	border-radius: 100px;
}
.btn-action:hover {
	background: #3eb0f7;
	color: white;
}
.btn-section {
	padding-top: 20px;
}
#feature {
	padding-top: 100px;
	background: #f6f6f6;
}
#feature h2 {
	margin: 15px 0 15px 0;
	font-size: 40px;
	font-weight: 300;
}
#feature .feature-title p {
	font-size: 18px;
}
.row-feat {
	padding-top: 50px;
}
.feat-list {
	margin-top: 40px;
}
.feat-list i {
	font-size: 48px;
	float: left;
	width: 20%;
	color: #555;
	height: 100%;
	position: relative;
	opacity: 0.6;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
.feat-list:hover i {
	color: #3eb0f7;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
#feature .inner {
	float: left;
	display: inline-block;
	width: 80%;
}
#feature-2 {
	padding: 100px 0 100px;
}
#feature-2 h2 {
	font-size: 40px;
	font-weight: 300;
	margin: 15px 0 15px 0;
}
.feature-2-pic {
	margin-top: 20px;
}
.subscribe {
	color: #fff;
}
.letter {
	margin-top: 100px;
	width: 100px;
	height: 100px;
	line-height: 100px;
	font-size: 50px;
	color: #3eb0f7;
	border-radius: 50%;
	border: solid 1px #3eb0f7;
}
.subscribe p {
	margin: 30px auto 30px;
}
.subscribe-form {
	max-width: 400px;
	margin: 50px auto 150px;
	text-align: center;
	overflow: hidden;
}
.subscribe-form form {
	position: relative;
}
.subscribe-form input {
	max-width: 85%;
	position: relative;
	padding: 5px 25px;
}
.subscribe-form .form-control {
	border-radius: 4px 0 0 4px;
	border: none;
	background-color: rgba(255, 255, 255, 0.6);
	color: #333;
	font-size: 1.2em;
	height: 55px;
}
.subscribe-form button {
	border-radius: 0 4px 4px 0;
	background-color: #3eb0f7;
	color: #ffffff;
	font-size: 1em;
	line-height: 52px;
	position: absolute;
	top: 0px;
	right: 0px;
	padding: 0 30px;
	margin: 0;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
.subscribe-form .btn {
	height: 55px;
}
.subscribe-form .btn:hover {
	background-color: #1f96e0;
	color: #fff;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
.subscribe-form .form-control::-webkit-input-placeholder {
	color: #333;
}
.subscribe-form .form-control:-moz-placeholder { 
	color: #333;  
}
.subscribe-form .form-control::-moz-placeholder { 
	color: #333;  
}
.subscribe-form .form-control:-ms-input-placeholder {  
	color: #333;  
}
#package {
	padding-top: 100px;
}
.title-line {
    width: 100px;
    height: 3px;
    margin: 0 auto;
    background: #3eb0f7;
}
.price-box {
	border: solid 1px #d1d1d1;
}
.package-option {
	padding: 50px 0 100px;
}
.package-option ul {
	padding: 0;
}
.price-heading h3 {
	margin-top: 0;
}
.price-heading i {
	color: #d1d1d1;
	font-size: 75px;
	margin-top: 20px;
}
.price-group {
	padding: 30px 0 10px;
}
.price-group .dollar {
	font-size: 20px;
	position: relative;
	bottom: 48px;
}
.price-group .price {
	color: #3eb0f7;
	font-size: 90px;
	font-weight: 500;
}
.price-group .time {
	font-size: 18px;
}
.price-feature li {
	margin-left: 30px;
	margin-right: 30px;
	list-style: none;
	border-bottom: solid 1px #d1d1d1;
	line-height: 40px;
}
.btn-price {
	margin: 5px 0 15px;
	font-size: 17px;
	padding: 7px 35px;
	height: 40px;
	background: #3eb0f7;
	transition: all 0.4s;
	color: white;
	border-radius: 4px;	
}
.btn-price:hover {
	background: #1f96e0;
	color: white;
}
#client {
	background: #f6f6f6;
	padding: 70px 0 70px;
}
#client img {
	max-height: 50px;
	margin: 0 20px;
	opacity: 1;
	transition: all 0.3s ease;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
}
#client img:hover {
	opacity: 0.7;
	transition: all 0.3s ease;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
}
#testi {
	padding: 100px 0 100px;
}
.testi-item {
	display: block;
	width: 100%;
	height: auto;
	position: relative;
	margin-top: 30px;
}
.testi-item .box {
	margin-right: 15px;
	margin-left: 15px;
}
.testi-item .box .message {
	padding: 20px;
	font-style: italic;
	line-height: 30px;
	font-weight: 300;
	font-size: 20px;
}
.testi-item .client-pic img {
	width: 70px;
	height: 70px;
	border-radius: 50%;
	max-width: 100%;
}
.testi-item .client-info .client-name {
	margin-top: 10px;
	font-size: 16px;
}
.testi-item .client-info .company {
	font-style: italic;
	color: #3eb0f7;
}
.action {
	color: #fff;
}
.action h2 {
	margin-top: 100px;
	font-size: 40px;
	font-weight: 300;
}
.download-store {
	padding: 50px 0 130px;
}
.download-store li {
	display: inline-block;
	list-style: none;
	margin-left: 10px;
}
.appstorebutton {
	height: 65px;
	width: 225px;
	position: relative;
	border-radius: 4px;
} 
.iphone {
	background: #3eb0f7;
	transition: all 0.3s ease;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
}
.iphone:hover {
	background-color: #1f96e0;
	transition: all 0.3s ease;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
}
.android {
	background: #555;
	transition: all 0.3s ease;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
}
.android:hover {
	background-color: #444;
	transition: all 0.3s ease;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
}
.appstorebutton a {
	color: white;
	text-decoration: none;
}
.appstorebutton p {
	font-size: 20px;
	font-weight: 300;
	text-align: left;
	color: white;
	padding: 20px 0 0 70px;
	line-height: 1;
}
.appstorebutton p small {
	font-size: 16px;
}
.appstorebutton i {
	position: absolute;
	top: 0;
	left: 0;
	margin: 10px 0 0 17px;
	font-size: 40px;
}
#contact {
	width: 100%;
	min-height: 100%;
}
#contact  h2{
	color: white;
}
.ul-address a {
	font-weight: normal;
}
.ul-address a:hover {
	color: white;
}
.ul-address li {
	padding-right: 20px; 
	margin-bottom: 8px; 
	list-style: none;
	color: white;
}
.ul-address i {
	margin-left: 15px;
	position: absolute; 
	left: 0;
	color: #3eb0f7; 
	font-size: 25px;
	line-height: 30px;
 }
.contact-row {
	margin: 100px 0 100px;
}
#contact-form {
	margin: 0 auto;
}

#contact-form input {
	position: relative;
	padding: 5px 25px;
	width: 100%;
}
#contact-form textarea {
	position: relative;
	padding: 10px 25px;
	width: 100%;
	height: 120px !important;
}
#contact-form .form-control {
	border-radius: 0;
	border: solid 1px #dadada;
	background-color: #fff;
	color: #333;
	font-size: 1.2em;
	height: 55px;
}
#contact-form .btn {
	height: 55px;
	width: 100%;
	background-color: #3eb0f7;
	color: #ffffff;
	font-size: 17px !important;
	line-height: 18px;
	padding: 0 33px;
	border: none;
	border-radius: 0;
	margin: 0;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
#contact-form .btn:hover {
	background-color: #1f96e0;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
#footer {
	background: #fff;
	padding: 50px 0 50px;
}
.social ul {
	padding: 0;
	list-style: none;
}
.social li {
	display: inline-block;
	padding-right: .3em;
}
.social li a {
	display: block;
	width: 40px;
	height: 40px;
	line-height: 40px;
	color: #3eb0f7;
	border-radius: 50%;
	background: #fff;
	border: solid 1px #3eb0f7;
	-webkit-transition: all .8s ease;
	transition: all .8s ease;
}
.social li a:hover {
	color: #fff;
	background: #3eb0f7;
}
.scrollToTop{
	width: 40px; 
	height: 40px;
	padding: 5px;
	font-size: 30px;
	text-align: center; 
	background: rgba(0, 0, 0, 0.2);
	color: white;
	position: fixed;
	bottom: 20px;
	right: 20px;
	border-radius: 50%;
	display: none;
}
.scrollToTop:hover{
	color: #3eb0f7;
}
.control-group .controls {
	overflow-x: hidden;
}
/* iPads (portrait) ----------- */
@media only screen 
and (min-width : 768px) 
and (max-width : 1024px) 
and (orientation : portrait) {
	.more {
		margin-bottom: 50px;
	}
	.signup-header {
		margin: 100px 0 140px;
	}
	.price-feature li {
		margin-left: 15px;
		margin-right: 15px;
	}
	.btn-price {
		padding: 7px 25px;
	}
	#intro {
		padding: 30px 0 50px;
	}
	#intro h2, #feature-2 h2 {
		font-weight: 150;
		text-align: center;
	}
	#intro p, #feature-2 p {
		text-align: center;
	}
	.btn-section {
		text-align: center;
	}
	#feature {
		padding-top: 75px;
	}
	.row-feat {
		padding-top: 40px;
		padding-bottom: 40px;
	}
	#feature-2 {
		padding: 80px 0 100px;
	}
	.feature-2-pic {
		margin-top: 40px;
	}
	#client img {
		margin: 10px 0 10px;
		max-height: 40px;
	}	
}

/* iPads (landscape) ----------- */
@media only screen 
and (min-width : 768px) 
and (max-width : 1024px) 
and (orientation : landscape) {
	.logo {
		margin: 100px 0 30px 0;
	}
	.landing h1{
		margin: 60px 0 60px 0;
	}	
	.landing-text {
		margin: 60px 0 40px 0;
	}
	.more {
		margin-bottom: 180px;
	}
	.signup-header {
		margin-top: 170px;
	}
	.intro-pic {
		margin-top: 100px;
	}
	.feature-img {
		margin-top: 30px;
	}	
	.feat-list {
		margin-top: 10px;
	}
	.feat-list p {
		font-size: 15px;
	}
	.feature-2-pic {
		margin-top: 50px;
	}
	#client img {
		max-height: 45px;
	}	
}

/* smart-phone ----------- */
@media (max-width: 767px) {
	.logo {
		margin: 40px 0 30px 0;
	}
	.more {
		margin-bottom: 40px;
	}
	#intro {
		padding: 70px 0 70px;
	}
	#intro h2, #feature-2 h2 {
		font-weight: 150;
		text-align: center;
	}
	#intro p, #feature-2 p {
		text-align: center;
	}
	.btn-section {
		text-align: center;
	}
	.feat-list i {
		padding-left: 40px;
	}
	.feature-2-pic {
		margin-top: 50px;
	}
	.price-box {
		margin-bottom: 30px;
	}
	#client img {
		margin: 10px 0 10px;
	}	
	.contact-left {
		margin-bottom: 50px;
	}
}	
/* iPhone 6Plus (landscape) ----------- */
@media (max-width: 736px) {

}

/* iPhone 6 (landscape) ----------- */
@media (max-width: 667px) {
	.logo {
		margin: 30px 0 30px 0;
	}
	.landing h1{
		font-size: 50px;
		margin: 10px 0 25px 0;
	}
	.landing-text {
		margin-top: 20px;
	}
	.letter {
		margin-top: 75px
	}
	.subscribe p {
		margin: 25px auto 15px;
	}
	.subscribe-form {
		margin: 20px auto 100px;
	}
	.action h2 {
		margin-top: 70px;
	}
	.download-store {
		padding: 20px 0 100px;
	}
}

/* iPhone 5 (landscape) ----------- */
@media (max-width: 568px){
	.logo {
		margin: 15px 0 15px 0;
	}
	.landing h1{
		font-size: 40px;
		margin: 20px 0 10px 0;
	}
	.landing-text p {
		font-size: 16px !important; 
	}
	#intro {
		padding-top: 50px;
	}
	#feature {
		padding-top: 50px;
	}
	.feat-list i {
		padding-left: 20px;
	}
	#feature-2 {
		padding: 50px 0 70px;
	}
	#screenshot {
		padding-top: 70px;
	}	
	.screenshots {
		padding-top: 30px;
		padding-bottom: 70px;
	}
	#testi {
		padding: 70px 0 70px;
	}
	.testi-item .box .message {
		line-height: 25px;
		font-size: 18px;
	}
	.action h2 {
		margin: 50px 0 20px;
	}
	.download-text p {
		line-height: 1.5;	
	}	
	.download-store {
		padding: 10px 0 70px;
	}
}

/* iPhone 4 (landscape) ----------- */
@media (max-width: 480px) {
	.feat-list i {
		padding-left: 5px;
	}
	.action h2 {
		font-size: 30px;
	}
}

/* smart phone width: 360px ----------- */
@media (max-width: 360px) {
	#intro h2, #feature h2, #feature-2 h2 {
		font-size: 30px;
	}
	.feat-list i {
		padding-left: 0;
	}
	#feature .inner {
		padding-left: 15px;
	}
	.appstorebutton {
		margin-bottom: 15px;
	} 
	.download-store li {
		margin-left: 0;
	}
}

/* iPhone 4/5 (portrait) ----------- */
@media (max-width: 320px) {
	.logo {
		margin: 30px 0 20px 0;
	}
	.landing h1{
		margin: 20px 0 35px 0;
	}
	.more {
		text-align: center;
	}
	.more p {
		font-size: 14px;
	}
	.option {
		padding: 3px;
		min-width: 75px;
		margin-right: 2px;
	}
	.action-glass {
		font-size: 16px;
		margin: 30px 0 10px;
		padding: 15px 30px;
		height: 35px;
		border-radius: 80px;
	}
	.action-blue {
		font-size: 16px;
		margin: 30px 0 10px;
		margin-right: 15px;
		padding: 15px 30px;
		height: 35px;
		border-radius: 80px;
	}
	.subscribe-form {
		margin: 50px auto 110px;
	}
	.subscribe-form input {
		padding: 5px 15px;
	}
	.subscribe-form button {
		padding: 0 15px;
	}
	.action h2 {
		margin: 70px 0 20px;
	}

}