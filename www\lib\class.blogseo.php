<?php
class blogSeo {
  
  private $pageId;
  private $prefix;
  private $pagekeyw;
  private $pagekeyw_en;
  private $pagekeyw_de;
  private $pagekeyw_fr;
  private $pagekeyw_ru;
  private $pagekeyw_def;
  private $pagedesc;
  private $pagedesc_en;
  private $pagedesc_de;
  private $pagedesc_fr;
  private $pagedesc_ru;
  private $pagedesc_def;
  public $pagetitle;
  public $pagetitle_en;
  public $pagetitle_de;
  public $pagetitle_fr;
  public $pagetitle_ru;
  public $pagetitle_def;
  private $pagetext;
  private $pagetext_en;
  private $pagetext_de;
  private $pagetext_fr;
  private $pagetext_ru;
  private $pagetext_def;
  private $pagemeta;
  private $anchor;
  private $extrameta;
  private $slug;
    
  function __construct($id = false, $prefix) {
  $this->prefix = $prefix;
   if ( $id != false ) {
	  $this->pageId = $id;
	 } else {
	  $prms = explode("/", $_GET ['params']);
    $prms = array_filter($prms);
	  $this->pageId = $prms [0];
	  /*$subdir  = substr(realpath(dirname(__FILE__)), strlen(realpath($_SERVER['DOCUMENT_ROOT'])));
    $tmp_array = explode('?', trim($_SERVER['REQUEST_URI']));
    $URIParts = explode("/", $tmp_array[0]);
    $fl = $URIParts[1];
    $parameter1 = $URIParts[2];
	  $this->pageId = $parameter1;*/
	 }
  }
  
  public function getPageData() {
   $mysql = connecti();
	 if ( is_numeric($this->pageId) ) {
	  $sql_pageseo = "Select * From ".$this->prefix."page Where page_id = '$this->pageId'";
	 } else {
	  $sql_pageseo = "Select * From ".$this->prefix."page Where page_slug = '$this->pageId'";
	 }
   $ret_pageseo = $mysql->query ($sql_pageseo);
   //print $sql_pageseo.'<br />';
   //print mysql_num_rows($ret_pageseo);
   while ( $pgs = $ret_pageseo->fetch_array (MYSQLI_ASSOC) ) {
      $this->pagekeyw = $pgs ['page_keyw'];
      $this->pagekeyw_en = $pgs ['page_keyw_en'];
      $this->pagekeyw_de = $pgs ['page_keyw_de'];
      $this->pagekeyw_fr = $pgs ['page_keyw_fr'];
      $this->pagekeyw_ru = $pgs ['page_keyw_ru'];
      $this->pagekeyw_def = $pgs ['page_keyw'.$GLOBALS['site']->getLangDefKod()];
      $this->pagedesc = $pgs ['page_desc'];
      $this->pagedesc_en = $pgs ['page_desc_en'];
      $this->pagedesc_de = $pgs ['page_desc_de'];
      $this->pagedesc_fr = $pgs ['page_desc_fr'];
      $this->pagedesc_ru = $pgs ['page_desc_ru'];
      $this->pagedesc_def = $pgs ['page_desc'.$GLOBALS['site']->getLangDefKod()];
      $this->pagetitle = $pgs ['page_title'];
      $this->pagetitle_en = $pgs ['page_title_en'];
      $this->pagetitle_de = $pgs ['page_title_de'];
      $this->pagetitle_fr = $pgs ['page_title_fr'];
      $this->pagetitle_ru = $pgs ['page_title_ru'];
      $this->pagetitle_def = $pgs ['page_title'.$GLOBALS['site']->getLangDefKod()];
      $this->pagetext = $pgs ['page_text'];
      $this->pagetext_en = $pgs ['page_text_en'];
      $this->pagetext_de = $pgs ['page_text_de'];
      $this->pagetext_fr = $pgs ['page_text_fr'];
      $this->pagetext_ru = $pgs ['page_text_ru'];
      $this->pagetext_def = $pgs ['page_text'.$GLOBALS['site']->getLangDefKod()];
      $this->extrameta = $pgs ['page_extrameta'];
      $this->slug = $pgs ['page_slug'];
      $this->anchor = $pgs ['page_anchor'];
      //print $pgs ['mainpage_title'].' | '.$this->pagetitle;
   }
   $mysql->close;
  }
	   
  /*getters*/
   public function getKeyw() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return htmlspecialchars(stripslashes($this->pagekeyw));
     break;
    case 'de':
     return htmlspecialchars(stripslashes($this->pagekeyw_de));
     break;
    case 'en':
     return htmlspecialchars(stripslashes($this->pagekeyw_en));
     break;
    case 'fr':
     return htmlspecialchars(stripslashes($this->pagekeyw_fr));
     break;
    case 'ru':
     return htmlspecialchars(stripslashes($this->pagekeyw_ru));
     break;
    default:
     return htmlspecialchars(stripslashes($this->pagekeyw_def));
     break;
   }
  }
  public function getDesc() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return htmlspecialchars(stripslashes($this->pagedesc));
     break;
    case 'de':
     return htmlspecialchars(stripslashes($this->pagedesc_de));
     break;
    case 'en':
     return htmlspecialchars(stripslashes($this->pagedesc_en));
     break;
    case 'fr':
     return htmlspecialchars(stripslashes($this->pagedesc_fr));
     break;
    case 'ru':
     return htmlspecialchars(stripslashes($this->pagedesc_ru));
     break;
    default:
     return htmlspecialchars(stripslashes($this->pagedesc_def));
     break;
   }
  }
  public function getTitle() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return htmlspecialchars(stripslashes($this->pagetitle));
     break;
    case 'de':
     return htmlspecialchars(stripslashes($this->pagetitle_de));
     break;
    case 'en':
     return htmlspecialchars(stripslashes($this->pagetitle_en));
     break;
    case 'fr':
     return htmlspecialchars(stripslashes($this->pagetitle_fr));
     break;
    case 'ru':
     return htmlspecialchars(stripslashes($this->pagetitle_ru));
     break;
    default:
     return htmlspecialchars(stripslashes($this->pagedesc_def));
     break;
   }
  }
  /*public function getText() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->pagetext);
     break;
    case 'de':
     return stripslashes($this->pagetext_de);
     break;
    case 'en':
     return stripslashes($this->pagetext_en);
     break;
    case 'fr':
     return stripslashes($this->pagetext_fr);
     break;
    case 'ru':
     return stripslashes($this->pagetext_ru);
     break;
    default:
     return stripslashes($this->pagetext_def);
     break;
   }
  }*/
  
  public function getText() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     //return stripslashes($this->pagetext);
     $txt = stripslashes($this->pagetext);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    case 'de':
     //return stripslashes($this->pagetext_de);
     $txt = stripslashes($this->pagetext_de);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    case 'en':
     //return stripslashes($this->pagetext_en);
     $txt = stripslashes($this->pagetext_en);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    case 'fr':
     //return stripslashes($this->pagetext_fr);
     $txt = stripslashes($this->pagetext_fr);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    case 'ru':
     //return stripslashes($this->pagetext_ru);
     $txt = stripslashes($this->pagetext_ru);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    default:
     //return stripslashes($this->pagetext);
     $txt = stripslashes($this->pagetext_def);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
   }
  }
  
  public function getAnchor() {
   return $this->anchor;
  }
  public function getExtrameta() {
   return stripslashes($this->extrameta);
  }
  public function getSlug() {
   return stripslashes($this->slug);
  }
}

?>
