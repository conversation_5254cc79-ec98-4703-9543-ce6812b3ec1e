<?php
/*-----------------------------------------------------------------------------
#           <PERSON><PERSON><PERSON> port<PERSON> configur<PERSON><PERSON>llomány és függvénytár
#    Az oldal konfigurációja az oldal beállításai és adatbázis kapcsolat
#                          részekben tehető meg.
#       FONTOS! A nem megfelelő adatok megadása hibákhoz vezethet!
#
#         <PERSON><PERSON><PERSON> (e-lias), Armamenta Bt. <EMAIL>
#         
-----------------------------------------------------------------------------*/
 
//oldal beállításai
 
error_reporting(E_ALL);
session_start();

function connect_pdo() {
  $dsn = 'mysql:host=localhost;dbname=karcagu_furdo';
  $username = 'karcagu_user';
  $password = "KzNhqJCx";
  try {
    $pdo = new PDO($dsn, $username, $password);
    $pdo->exec("Set names utf8");
    $pdo->exec("set session sql_mode=replace(@@sql_mode,'STRICT_TRANS_TABLES','')");
  } catch (PDOException $e) {
    echo 'Connection failed: ' . $e->getMessage();
  }
  return $pdo;
}
//KzNhqJCx - karcagu_user

switch ($_COOKIE ['lang']) {
  case 'hu':
    include 'hu.php';
    break;
  case 'en':
    include 'en.php';
    break;
  case 'de':
    include 'de.php';
    break;
  case 'ro':
    include 'ro.php';
    break;
  case 'pl':
    include 'pl.php';
    break;
  default:
    include 'hu.php';
    break;
}

function checklang() {
  switch ($_COOKIE ['lang']) {
    case 'hu':
      return 'hu';
      break;
    case 'en':
      return 'en';
      break;
    case 'de':
      return 'de';
      break;
    case 'ro':
      return 'ro';
      break;
    case 'pl':
      return 'pl';
      break;
    default:
      return 'hu';
      break;
  }
}

$contactmail = '';      //az oldal kapcsolattartásra használt e-mail címe.
$site_url = 'http://www.akacligetfurdo.hu';         //az oldal címe
$konyvtar_hirkep = "/home/<USER>/domains/akacligetfurdo.hu/public_html/hirkep/";       //képfájlok mappája
$konyvtar_galkep = "/home/<USER>/domains/akacligetfurdo.hu/public_html/gal/";       //képfájlok mappája
$konyvtar_video = "/home/<USER>/domains/akacligetfurdo.hu/public_html/video/";       //képfájlok mappája
$konyvtar_files = "/home/<USER>/domains/akacligetfurdo.hu/public_html/uploads/";       //képfájlok mappája
$konyvtar_slides = "/home/<USER>/domains/akacligetfurdo.hu/public_html/slides/";       //képfájlok mappája
$konyvtar_szobak = "/home/<USER>/domains/akacligetfurdo.hu/public_html/szobak/";       //képfájlok mappája
$konyvtar_prog = "/home/<USER>/domains/akacligetfurdo.hu/public_html/prog/";       //képfájlok mappája
$konyvtar_akc = "/home/<USER>/domains/akacligetfurdo.hu/public_html/akc/";       //képfájlok mappája
$konyvtar_banner = "/home/<USER>/domains/akacligetfurdo.hu/public_html/banner/";       //képfájlok mappája
$sitename = 'Akácliget Fürdő Karcag';
//változók
 
$lightbox = '<link rel="stylesheet" href="css/lightbox.css" type="text/css" media="screen" />
 
<script type="text/javascript" src="js/prototype.js"></script>
<script type="text/javascript" src="js/scriptaculous.js?load=effects,builder"></script>
<script type="text/javascript" src="js/lightbox.js"></script>';

$lightbox2 = '
<link href="css/lightbox.css" rel="stylesheet" />
<script src="js/jquery-1.7.2.min.js"></script>
<script src="js/lightbox.js"></script>
';

function adminmenu() {
  connect();
  $admenu = '
  <ul class="dropdown">
      <li><a href="fooldal_ad.php">Főoldal</a></li>
      <li><a href="#">További aloldalak</a>
      <ul>
				<li><a href="contact_ad.php">Kapcsolat</a></li>
				<li><a href="termalfurdo_ad.php">Termál és élményfürdő</a></li>
				<li><a href="informacio_ad.php">Információk</a></li>
				<li>
				 <a href="strand_ad.php">Strand és Gyógyfürdő</a>
				 </li>
				  <li><a href="gyogyviz_ad.php"> - Gyógyvíz</a></li>
				  <li><a href="kezelesek_ad.php"> - Kezelések</a></li>
				  <li><a href="orvosi_ad.php"> - Orvosi ajánlás</a></li>
				  <li><a href="kura_ad.php"> - Gyógyászati árak</a></li>
				  <li><a href="gyogyaszat_ad.php"> - Gyógyászat</a>
				
				</li>
				<li><a href="szolg_ad.php">Szolgáltatások</a></li>
				<li><a href="kozerdeku_ad.php">Közérdekű adatok</a></li>
				<li>
				 <a href="szallashelyek_ad.php">Szálláshelyek</a>
				 <br /><br />
				  <a href="apartmanhaz_ad.php"> - Apartmanház</a><br />
				  <a href="vendegszoba_ad.php"> - Vendégszoba</a><br />
				  <a href="fahaz_ad.php"> - Faház</a><br />
				  <a href="lakokocsi_ad.php"> - Lakókocsi beállóhely</a><br />
				  <a href="satorhely_ad.php"> - Sátorhely</a><br />
				</li>
				<li><a href="tamogatok_ad.php">Támogatók</a></li>
                        <li><a href="palyazat_ad.php">Pályázat</a></li>
                        <li><a href="palyazat2_ad.php">Pályázat, gyógymedence</a></li>
                        <li><a href="adatkezelesi_ad.php">Adatkezelési</a></li>
			 </ul>
			</li>
			
			<li>
			 <a href="hirek_ad.php">Hírek</a>
			</li>
			<li>
			 <a href="galeriak_ad.php">Galériák</a>
			</li>
			<li>
			 <a href="videok_ad.php">Videók</a>
			</li>
			<li>
			 <a href="programok_ad.php">Programok</a>
			</li>
			<li>
			 <a href="file_upload.php">File feltöltés</a>
			</li>
			<!--<li>
			 <a href="slider_ad.php">Slider képek</a>
			</li>-->
      <li>
			 <a href="akciok_ad.php">Árak, akciók</a>
			</li>
			<li>
			 <a href="szolgaltatasok_ad.php">Szolgáltatások</a>
			</li>
			<li>
			 <a href="szallasok_ad.php">Szállások</a>
			</li>
			<li>
			 <a href="bannerek_ad.php">Bannerek</a>
			</li>
			<li><a href="foglalasok_ad.php">Foglalások';
				$sql_selfogl = "Select * From foglalas Where foglalas_visszaigazolt = '0'";
        $ret_selfogl = mysql_query ($sql_selfogl);
				if ( mysql_num_rows ($ret_selfogl) >= 1  ) {
				 $admenu .= ' ( '.mysql_num_rows ($ret_selfogl).' )';
				} else {
				 $admenu .= ' ( 0 )';
				}				
				 
				$admenu .=	'</a></li>
			<li>
			 <a href="hirlev.php">Hírlevél</a>
			 <ul>
				<li><a href="hirlev.php">Hírlevelek</a></li>
				<li><a href="feliratk_ad.php">Feliratkozottak</a></li>
			 </ul>
			</li>
     </ul>
     <div style="clear: both;"></div>';
  print $admenu;
}

$GAcode = '<script>
  (function(i,s,o,g,r,a,m){i[\'GoogleAnalyticsObject\']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,\'script\',\'//www.google-analytics.com/analytics.js\',\'ga\');

  ga(\'create\', \'UA-31422877-8\', \'karcagfurdo.hu\');
  ga(\'send\', \'pageview\');

</script>';

$menu = '
      <a href="index.php" class="">'.$GLOBALS ['_menu_fooldal'].'</a>
			<a href="hirek.php" class="">'.$GLOBALS ['_menu_hirek'].'</a>
			<a href="programok.php" class="">'.$GLOBALS ['_menu_progr'].'</a>
			<a href="tamogatok.php" class="">'.$GLOBALS ['_menu_tamogat'].'</a>
			<a href="kapcsolat.php" class="">'.$GLOBALS ['_menu_kapcsolat'].'</a>
                  <a href="adatkezelesitajekoztato.php" class="">Adatkezelési tájékoztató</a>
			';

$menu_strand = '<a href="index.php" class="">'.$GLOBALS ['_menu_fooldal'].'</a>
      <a href="hirek.php" class="">'.$GLOBALS ['_menu_hirek'].'</a>
			<a href="programok.php" class="">'.$GLOBALS ['_menu_progr'].'</a>
			<a href="tamogatok.php" class="">'.$GLOBALS ['_menu_tamogat'].'</a>
      <a href="arakakciok.php" class="">'.$GLOBALS ['_menu_akciok'].'</a>
      <a href="szolgaltatasok.php" class="">'.$GLOBALS ['_menu_szolg'].'</a>
      <a href="galeria.php" class="">'.$GLOBALS ['_menu_galeria'].'</a>
		  <a href="kapcsolat.php" class="">'.$GLOBALS ['_menu_kapcsolat'].'</a>';

$menu_gyogy = '<a href="index.php" class="">'.$GLOBALS ['_menu_fooldal'].'</a>
      <a href="hirek.php" class="">'.$GLOBALS ['_menu_hirek'].'</a>
			<a href="programok.php" class="">'.$GLOBALS ['_menu_progr'].'</a>
			<a href="tamogatok.php" class="">'.$GLOBALS ['_menu_tamogat'].'</a>
			<a href="gyogyviz.php" class="">'.$GLOBALS ['_menu_gyogyviz'].'</a>
			<a href="gyogykezelesek.php" class="">'.$GLOBALS ['_menu_gyogykez'].'</a>
			<a href="orvosiajanlas.php" class="">'.$GLOBALS ['_menu_orvosi'].'</a>
			<a href="tbkura.php" class="">'.$GLOBALS ['_menu_tbkura'].'</a>
			';

$menu_szallas = '<a href="index.php" class="">'.$GLOBALS ['_menu_fooldal'].'</a>
      <a href="hirek.php" class="">'.$GLOBALS ['_menu_hirek'].'</a>
			<a href="programok.php" class="">'.$GLOBALS ['_menu_progr'].'</a>
			<a href="tamogatok.php" class="">'.$GLOBALS ['_menu_tamogat'].'</a>';
		  
$headerszov = '<h1>'.$GLOBALS ['_szlogen1'].'</h1>
    <h3>'.$GLOBALS ['_szlogen2'].'</h3>';
 
 //----------------------------MySQL Inject kivédés-----------------------
 
function esc_mysql ($arg) {
  $string = htmlspecialchars($arg);
  $string = str_replace ('"', '&quot;', $string );
  $string = str_replace ('\'', "\\'", $string );
  return $string;
}
 
/*---- Beléptetés ----*/
function login() {
  $pdo = connect_pdo();
  $nick = strtolower($_POST ['nick']);
	$pass = $_POST ['pass'];
	$lsub = $_POST ['belep'];
	//$adatmeg = $_POST ['adatmeg'];
	$ErrMsg;
	
	if ( $lsub != '' ) {
    $sql_getuser = "Select * From user Where LOWER(user_nick) = :nick";
    $statement = $pdo->prepare($sql_getuser);
    $statement->execute(['nick' => $nick]);
    $users = $statement->fetchAll(PDO::FETCH_ASSOC);
    $lgnum = count($users);
    if ( $lgnum == 1 ) {
      foreach ($users as $getuser) {
        if ( $pass == $getuser ['user_pass'] ) {
          $_SESSION ['mail'] = $getuser ['user_mail'];
          $_SESSION ['user'] = $getuser ['user_nick'];
          $_SESSION ['user_id'] = $getuser ['user_id'];
          $_SESSION ['logged'] = 1;
          header('Location: index.php');
        } else {
          $ErrMsg = 'Próbáld újra!';
          print $ErrMsg;
        }
      }
    } else {
      $ErrMsg = 'Nincs ilyen user baaaz!';
      print $ErrMsg;
    }
  } else {
    //printlogin($ErrMsg);
    // //print 'nosub';
  }
}
 
function logged() {
  if ( isset($_SESSION ['logged']) and $_SESSION ['logged'] == 1 ) {
    return true;
  } else {
    return false;
  }
}

function ifadmin() {
  if ( isset($_SESSION ['csoport']) and $_SESSION ['csoport'] == 1 ) {
    return true;
  } else {
    return false;
  }
}

function picupload($location, $location_rel, $nagyszel, $kisszel = 0, $kismagas = 0) {
  //sample: $array = picupload('/var/www/htdocs/keptar/', 'keptar/', '500', '100', '60');
  if ( $nagyszel == '' or $nagyszel == 0 ) {
    $nagyszel = 500;
  }
 
  $outpic = '';
  foreach($_FILES as $allomanynev => $all_tomb) { 
	  if (is_uploaded_file($all_tomb['tmp_name'])) {
	    if ( ( strstr($all_tomb['type'], 'jpeg') or strstr($all_tomb['type'], 'png') or strstr($all_tomb['type'], 'gif') ) ) {
        $all_tomb['name'] = str_replace('é', 'e', $all_tomb['name']);
        $all_tomb['name'] = str_replace('á', 'a', $all_tomb['name']);
        $all_tomb['name'] = str_replace('í', 'i', $all_tomb['name']);
        $all_tomb['name'] = str_replace('ű', 'u', $all_tomb['name']);
        $all_tomb['name'] = str_replace('ü', 'u', $all_tomb['name']);
        $all_tomb['name'] = str_replace('ú', 'u', $all_tomb['name']);
        $all_tomb['name'] = str_replace('ő', 'o', $all_tomb['name']);
        $all_tomb['name'] = str_replace('ö', 'o', $all_tomb['name']);
        $all_tomb['name'] = str_replace('ó', 'o', $all_tomb['name']);
        $all_tomb['name'] = str_replace('É', 'e', $all_tomb['name']);
        $all_tomb['name'] = str_replace('Á', 'a', $all_tomb['name']);
        $all_tomb['name'] = str_replace('Í', 'i', $all_tomb['name']);
        $all_tomb['name'] = str_replace('Ű', 'u', $all_tomb['name']);
        $all_tomb['name'] = str_replace('Ü', 'u', $all_tomb['name']);
        $all_tomb['name'] = str_replace('Ú', 'u', $all_tomb['name']);
        $all_tomb['name'] = str_replace('Ő', 'o', $all_tomb['name']);
        $all_tomb['name'] = str_replace('Ö', 'o', $all_tomb['name']);
        $all_tomb['name'] = str_replace('Ó', 'o', $all_tomb['name']);
        $all_tomb['name'] = str_replace(' ', '_', $all_tomb['name']);
        $all_tomb['name'] = str_replace('%', '_', $all_tomb['name']);
        $all_tomb['name'] = str_replace(' ', '_', $all_tomb['name']);
        $all_tomb['name'] = str_replace("\'", '_', $all_tomb['name']);
        $all_tomb['name'] = str_replace(":", '_', $all_tomb['name']);
        
        move_uploaded_file($all_tomb['tmp_name'], $location.$all_tomb['name']) or die ("ERROR!");
        chmod($location.$all_tomb['name'], 0777);
        $outpic [] = $all_tomb['name'];
        $op = $all_tomb['name'];
        if ( strstr($all_tomb['type'], 'jpeg') ) {
          $im2 = ImageCreateFromJpeg($location.$op);
          list($width2, $height2) = getimagesize($location.$op);
          $new_width2 = $nagyszel;
          $new_height2 = ( $new_width2 / $width2 ) * $height2;
          $image_p2 = imagecreatetruecolor($new_width2, $new_height2);
          imagecopyresampled($image_p2, $im2, 0, 0, 0, 0, $new_width2, $new_height2, $width2, $height2);
          $filename2 = $location.$op;
          imagejpeg($image_p2,$filename2,80);
          imagedestroy($im2);
          imagedestroy($image_p2);
          chmod($location.$op, 0777);
          
          if ( $kisszel != '' or $kisszel != 0 ) {
            $im = ImageCreateFromJpeg($location.$op);
            list($width, $height) = getimagesize($location.$op);
            $new_width = $kisszel;
            $new_height = ( $new_width / $width ) * $height;
            if ($new_height > $kismagas) {
              $new_height = $kismagas;
              $new_width = ( $new_height / $height ) * $width;
            }
            $image_p = imagecreatetruecolor($new_width, $new_height);
            imagecopyresampled($image_p, $im, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
            $filename = $location."tn_".$op;
            imagejpeg($image_p,$filename,70);
            imagedestroy($im);
            imagedestroy($image_p);
            chmod($location.'tn_'.$op, 0777);
          }
        }
        
        if ( strstr($all_tomb['type'], 'gif') ) {
          if ( $kisszel != '' or $kisszel != 0 ) {
            $im = ImageCreateFromGif($location.$op);
            list($width, $height) = getimagesize($location.$op);
            $new_width = $kisszel;
            $new_height = ( $new_width / $width ) * $height;
            if ($new_height > $kismagas) {
              $new_height = $kismagas;
              $new_width = ( $new_height / $height ) * $width;
            }
            $image_p = imagecreatetruecolor($new_width, $new_height);
            imagecopyresampled($image_p, $im, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
            $filename = $location."tn_".$op;
            imagegif($image_p,$filename,100);
            imagedestroy($im);
            imagedestroy($image_p);
            chmod($location.'tn_'.$op, 0777);
          }
          $im2 = ImageCreateFromGif($location.$op);
          list($width2, $height2) = getimagesize($location.$op);
          $new_width2 = 500;
          $new_height2 = ( $new_width2 / $width2 ) * $height2;
          $image_p2 = imagecreatetruecolor($new_width2, $new_height2);
          imagecopyresampled($image_p2, $im2, 0, 0, 0, 0, $new_width2, $new_height2, $width2, $height2);
          $filename2 = $location.$op;
          imagegif($image_p2,$filename2,100);
          imagedestroy($im2);
          imagedestroy($image_p2);
          chmod($location.$op, 0777);
        }
        if ( strstr($all_tomb['type'], 'png') ) {
          if ( $kisszel != '' or $kisszel != 0 ) {
            $im = ImageCreateFromPng($location.$op);
            list($width, $height) = getimagesize($location.$op);
            $new_width = $kisszel;
            $new_height = ( $new_width / $width ) * $height;
            if ($new_height > $kismagas) {
              $new_height = $kismagas;
              $new_width = ( $new_height / $height ) * $width;
            }
            $image_p = imagecreatetruecolor($new_width, $new_height);
            imagecopyresampled($image_p, $im, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
            $filename = $location."tn_".$op;
            imagepng($image_p,$filename);
            imagedestroy($im);
            imagedestroy($image_p);
            chmod($location.'tn_'.$op, 0777);
          }
          $im2 = ImageCreateFromPng($location.$kep);
          list($width2, $height2) = getimagesize($location.$op);
          $new_width2 = 500;
          $new_height2 = ( $new_width2 / $width2 ) * $height2;
          $image_p2 = imagecreatetruecolor($new_width2, $new_height2);
          $filename2 = $location.$op;
          imagepng($image_p2,$filename2);
          imagedestroy($im2);
          imagedestroy($image_p2);
          chmod($location.$op, 0777);
        }
      } else {
        exit ('Nem megfelelő fileformátumot válaszottál!');
      }
    }
  }
  return $outpic;
}

function picupload_upload($kep, $location, $nagyszel, $kisszel = 0, $kismagas = 0) {
  //sample: $array = picupload('/var/www/htdocs/keptar/', 'keptar/', '500', '100', '60');
 
  if ( $nagyszel == '' or $nagyszel == 0 ) {
    $nagyszel = 500;
  }
  $op = str_replace($_SERVER['DOCUMENT_ROOT'],'',$kep);
  $op = basename($op);
  $outpic = '';
  
  if ( strstr(strtolower($kep), 'jpg') ) {
    $im2 = ImageCreateFromJpeg($kep);
    list($width2, $height2) = getimagesize($kep);
    $new_width2 = $nagyszel;
    $new_height2 = ( $new_width2 / $width2 ) * $height2;
    $image_p2 = imagecreatetruecolor($new_width2, $new_height2);
    imagecopyresampled($image_p2, $im2, 0, 0, 0, 0, $new_width2, $new_height2, $width2, $height2);
    $filename2 = $location.$op;
    imagejpeg($image_p2,$filename2,80);
    imagedestroy($im2);
    imagedestroy($image_p2);
    chmod($kep, 0777);
    if ( $kisszel != '' or $kisszel != 0 ) {
      $im = ImageCreateFromJpeg($kep);
      list($width, $height) = getimagesize($kep);
      $new_width = $kisszel;
      $new_height = ( $new_width / $width ) * $height;
      if ($new_height < $kismagas) {
        $new_height = $kismagas;
        $new_width = ( $new_height / $height ) * $width;
      }
      $image_p = imagecreatetruecolor($new_width, $new_height);
      imagecopyresampled($image_p, $im, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
      $filename = $location."tn_".$op;
      imagejpeg($image_p,$filename,70);
      imagedestroy($im);
      imagedestroy($image_p);
      chmod($location.'tn_'.$op, 0777); 
    }
  }
  if ( strstr(strtolower($kep), 'gif') ) {
    if ( $kisszel != '' or $kisszel != 0 ) {
      $im = ImageCreateFromGif($kep);
      list($width, $height) = getimagesize($kep);
      $new_width = $kisszel;
      $new_height = ( $new_width / $width ) * $height;
      if ($new_height < $kismagas) {
        $new_height = $kismagas;
        $new_width = ( $new_height / $height ) * $width;
      }
      $image_p = imagecreatetruecolor($new_width, $new_height);
      imagecopyresampled($image_p, $im, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
      $filename = $location."tn_".$op;
      imagegif($image_p,$filename,100);
      imagedestroy($im);
      imagedestroy($image_p);
      chmod($location.'tn_'.$op, 0777);
    }
    $im2 = ImageCreateFromGif($kep);
    list($width2, $height2) = getimagesize($kep);
    $new_width2 = 500;
    $new_height2 = ( $new_width2 / $width2 ) * $height2;
    $image_p2 = imagecreatetruecolor($new_width2, $new_height2);
    imagecopyresampled($image_p2, $im2, 0, 0, 0, 0, $new_width2, $new_height2, $width2, $height2);
    $filename2 = $location.$op;
    imagegif($image_p2,$filename2,100);
    imagedestroy($im2);
    imagedestroy($image_p2);
    chmod($location.$op, 0777);
  }
  if ( strstr(strtolower($kep), 'png') ) {
    if ( $kisszel != '' or $kisszel != 0 ) {
      $im = ImageCreateFromPng($kep);
      list($width, $height) = getimagesize($kep);
      $new_width = $kisszel;
      $new_height = ( $new_width / $width ) * $height;
      if ($new_height < $kismagas) {
        $new_height = $kismagas;
        $new_width = ( $new_height / $height ) * $width;
      }
      $image_p = imagecreatetruecolor($new_width, $new_height);
      imagecopyresampled($image_p, $im, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
      $filename = $location."tn_".$op;
      imagepng($image_p,$filename);
      imagedestroy($im);
      imagedestroy($image_p);
      chmod($location.'tn_'.$op, 0777);
    }
    $im2 = ImageCreateFromPng($kep);
    list($width2, $height2) = getimagesize($kep);
    $new_width2 = 500;
    $new_height2 = ( $new_width2 / $width2 ) * $height2;
    $image_p2 = imagecreatetruecolor($new_width2, $new_height2);
    $filename2 = $location.$op;
    imagepng($image_p2,$filename2);
    imagedestroy($im2);
    imagedestroy($image_p2);
    chmod($location.$op, 0777);
  }
}

function picupload2($location, $location_rel, $nagyszel, $kisszel = 0, $kismagas = 0) {
  //sample: $array = picupload('/var/www/htdocs/keptar/', 'keptar/', '500', '100', '60');
  if ( $nagyszel == '' or $nagyszel == 0 ) {
    $nagyszel = 500;
  }
  $outpic = '';
  foreach($_FILES as $allomanynev => $all_tomb) { 
    if (is_uploaded_file($all_tomb['tmp_name'])) {
      if ( ( strstr($all_tomb['type'], 'jpeg') or strstr($all_tomb['type'], 'png') or strstr($all_tomb['type'], 'gif') ) ) {
        $all_tomb['name'] = str_replace('é', 'e', $all_tomb['name']);
        $all_tomb['name'] = str_replace('á', 'a', $all_tomb['name']);
        $all_tomb['name'] = str_replace('í', 'i', $all_tomb['name']);
        $all_tomb['name'] = str_replace('ű', 'u', $all_tomb['name']);
        $all_tomb['name'] = str_replace('ü', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ú', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ő', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ö', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ó', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('É', 'e', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Á', 'a', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Í', 'i', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ű', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ü', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ú', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ő', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ö', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ó', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace(' ', '_', $all_tomb['name']);
           $all_tomb['name'] = str_replace('%', '_', $all_tomb['name']);           
           $all_tomb['name'] = str_replace(' ', '_', $all_tomb['name']);
           $all_tomb['name'] = str_replace("\'", '_', $all_tomb['name']);
           $all_tomb['name'] = str_replace(":", '_', $all_tomb['name']);
           //print $all_tomb['name']; 
    move_uploaded_file($all_tomb['tmp_name'], $location.$all_tomb['name']) or die ("ERROR!");
 		chmod($location.$cikkszam.'_'.$ido.$all_tomb['name'], 0777);
     $outpic [] = $all_tomb['name'];
     $op = $all_tomb['name'];
            
   } else {
    exit ('Nem megfelelő fileformátumot válaszottál!');
   }
	}
}
return $outpic;
}

function banner_main() {
 connect();
 print '<div class="bn">';
 $sql_selbanner = "Select * From banner Where banner_foszponzor = '1' and banner_aktiv = '1' Order By banner_id DESC Limit 1";
 $ret_selbanner = mysql_query ($sql_selbanner);
 while ($sb = mysql_fetch_array ($ret_selbanner)) {
  if ( strstr($sb ['banner_image'], '.swf') ) {
	 print '<object type="application/x-shockwave-flash" data="banner/'.$sb ['banner_image'].'">
      <param name="movie" value="banner/'.$sb ['banner_image'].'" />
      <param name="quality" value="high" />
      <param name="wmode" value="transparent" />
      <param name="flashvars" value="file=index" />
    </object><br />';
	} else {
	 print '<a href="'.$sb ['banner_url'].'" target="_blank"><img src="banner/'.$sb ['banner_image'].'" alt="" title="" /></a><br />';
	}
 }
 print '</dvi>';
 sutiwarn();
}

function banner() {
 connect();
 print '<div class="bn">';
 $sql_selbanner = "Select * From banner Where banner_foszponzor = '0' and banner_aktiv = '1' Order By RAND() Limit 1";
 $ret_selbanner = mysql_query ($sql_selbanner);
 while ($sb = mysql_fetch_array ($ret_selbanner)) {
  if ( strstr($sb ['banner_image'], '.swf') ) {
	 print '<object type="application/x-shockwave-flash" data="banner/'.$sb ['banner_image'].'">
      <param name="movie" value="banner/'.$sb ['banner_image'].'" />
      <param name="quality" value="high" />
      <param name="wmode" value="transparent" />
      <param name="flashvars" value="file=index" />
    </object><br />';
	} else {
	 print '<a href="'.$sb ['banner_url'].'" target="_blank"><img src="banner/'.$sb ['banner_image'].'" alt="" title="" /></a><br />';
	}
 }
 print '</dvi>';
}

function banner_all() {
 connect();
 print '<div class="bn">';
 $sql_selbanner = "Select * From banner Where banner_foszponzor = '0' and banner_aktiv = '1' Order By RAND()";
 $ret_selbanner = mysql_query ($sql_selbanner);
 while ($sb = mysql_fetch_array ($ret_selbanner)) {
  if ( strstr($sb ['banner_image'], '.swf') ) {
	 print '<object type="application/x-shockwave-flash" data="banner/'.$sb ['banner_image'].'">
      <param name="movie" value="banner/'.$sb ['banner_image'].'" />
      <param name="quality" value="high" />
      <param name="wmode" value="transparent" />
      <param name="flashvars" value="file=index" />
    </object><br />';
	} else {
	 print '<a href="'.$sb ['banner_url'].'" target="_blank"><img src="banner/'.$sb ['banner_image'].'" alt="" title="" /></a><br />';
	}
 }
 print '</dvi>';
}

function sutiwarn() {
 
 if ( $_COOKIE ['suti'] != 'ok' ) {
  print '<div id="suti" style="display: block;">
   <div class="container">
    <a href="javascript:sutiWarn();" class="sutigomb">Megértettem</a>
    <br />Az akacligetfurdo.hu weboldalon úgynevezett <a href="https://hu.wikipedia.org/wiki/HTTP-s%C3%BCti" target="_blank">sütiket</a> használunk a jobb felhasználói élmény és a weboldal működésének megfigyelése érdekében! Ha folytatja a böngészést weboldalunkon, akkor úgy tekintjük, hogy beleegyezik a sütik használatába! <a href="adatkezelesitajekoztato.php">További információk</a>   
   </div>
  </div><!-- süti -->';
 }
 
}