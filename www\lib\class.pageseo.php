<?php
class PageSeo {
  private $pageId;
  public $pageDbId;
  private $prefix;
  private $pagekeyw;
  private $pagekeyw_en;
  private $pagekeyw_de;
  private $pagekeyw_fr;
  private $pagekeyw_ru;
  private $pagekeyw_def;
  private $pagedesc;
  private $pagedesc_en;
  private $pagedesc_de;
  private $pagedesc_fr;
  private $pagedesc_ru;
  private $pagedesc_def;
  public $pagetitle;
  public $pagetitle_en;
  public $pagetitle_de;
  public $pagetitle_fr;
  public $pagetitle_ru;
  public $pagetitle_def;
  private $pagetext;
  private $pagetext_en;
  private $pagetext_de;
  private $pagetext_fr;
  private $pagetext_ru;
  private $pagetext_def;
  private $pagemeta;
  private $anchor;
  //private $extrameta;
  private $slug;
  private $aktdomain;
  function __construct($id = false, $prefix) {
  $this->prefix = $prefix;
   if ( $id != false ) {
    $this->pageId = $id;
    } else {
    $prms = explode("/", $_GET ['params']);
    $prms = array_filter($prms);
    $this->pageId = $prms [0];
    }
  }
  public function getPageData($domain) {
   $mysql = connecti();
    if ( is_numeric($this->pageId) ) {
    $sql_pageseo = "Select * From ".$this->prefix."mainpage Where mainpage_id = '$this->pageId' and mainpage_domain = '$domain' and mainpage_map = 1";
    } else {
    $sql_pageseo = "Select * From ".$this->prefix."mainpage Where mainpage_slug = '$this->pageId' and mainpage_domain = '$domain' and mainpage_map = 1";
    }
   $ret_pageseo = $mysql->query ($sql_pageseo);
   //print $sql_pageseo.'<br />';
   //print mysql_num_rows($ret_pageseo);
   while ( $pgs = $ret_pageseo->fetch_array (MYSQLI_ASSOC) ) {
      $this->pageDbId = $pgs ['mainpage_id'];
      $this->pagekeyw = $pgs ['mainpage_keyw'];
      $this->pagekeyw_en = $pgs ['mainpage_keyw_en'];
      $this->pagekeyw_de = $pgs ['mainpage_keyw_de'];
      $this->pagekeyw_fr = $pgs ['mainpage_keyw_fr'];
      $this->pagekeyw_ru = $pgs ['mainpage_keyw_ru'];
      $this->pagekeyw_def = $pgs ['mainpage_keyw'.$GLOBALS['site']->getLangDefKod()];
      $this->pagedesc = $pgs ['mainpage_desc'];
      $this->pagedesc_en = $pgs ['mainpage_desc_en'];
      $this->pagedesc_de = $pgs ['mainpage_desc_de'];
      $this->pagedesc_fr = $pgs ['mainpage_desc_fr'];
      $this->pagedesc_ru = $pgs ['mainpage_desc_ru'];
      $this->pagedesc_def = $pgs ['mainpage_desc'.$GLOBALS['site']->getLangDefKod()];
      $this->pagetitle = $pgs ['mainpage_title'];
      $this->pagetitle_en = $pgs ['mainpage_title_en'];
      $this->pagetitle_de = $pgs ['mainpage_title_de'];
      $this->pagetitle_fr = $pgs ['mainpage_title_fr'];
      $this->pagetitle_ru = $pgs ['mainpage_title_ru'];
      $this->pagetitle_def = $pgs ['mainpage_title'.$GLOBALS['site']->getLangDefKod()];
      $this->pagetext = $pgs ['mainpage_text'];
      $this->pagetext_en = $pgs ['mainpage_text_en'];
      $this->pagetext_de = $pgs ['mainpage_text_de'];
      $this->pagetext_fr = $pgs ['mainpage_text_fr'];
      $this->pagetext_ru = $pgs ['mainpage_text_ru'];
      $this->pagetext_def = $pgs ['mainpage_text'.$GLOBALS['site']->getLangDefKod()];
      $this->slug = $pgs ['mainpage_slug'];
      $this->anchor = $pgs ['mainpage_anchor'];
   }
   $mysql->close;
  }
  public function getSubPageList() {
    $mysql = connecti();
    $numid = '';
    if ( is_numeric($this->pageId) ) {
    $numid = $this->pageId;
    } else {
    $sql_pageseo = "Select mainpage_id From ".$GLOBALS['prefix']."mainpage Where mainpage_slug = '$this->pageId' and mainpage_map = 1";
    $ret_pageseo = $mysql->query ($sql_pageseo);
    while ( $nmi = $ret_pageseo->fetch_array (MYSQLI_ASSOC) ) {
    $numid = $nmi ['mainpage_id'];
    }
    }
    $listout = "";
    $sql_selsubpages = "Select * From ".$this->prefix."page Left Join ".$this->prefix."iconclass on iconclass_id = page_iconclass Where page_main = '$numid' and page_blokk = 1 and page_map = 1 Order By page_id ASC";
    $ret_selsubpages = $mysql->query ($sql_selsubpages);
    //print $sql_selsubpages;
    while ( $sbp = $ret_selsubpages->fetch_array (MYSQLI_ASSOC) ) {
    $listout .= '<div class="col-lg-4 col-md-6 col-sm-12 col-xs-12 feat-list">
      <i class="'.str_replace('.', '', $sbp ['iconclass_class']).' pe-5x pe-va"></i>
      <div class="inner">
       <h4><a href="aloldal/'.$sbp ['page_slug'].'/">';
    switch ($_COOKIE ['lang']) {
        case 'hu':
         $listout .= htmlspecialchars(stripslashes($sbp ['page_title']));
         break;
        case 'de':
         $listout .= htmlspecialchars(stripslashes($sbp ['page_title_de']));
         break;
        case 'en':
         $listout .= htmlspecialchars(stripslashes($sbp ['page_title_en']));
         break;
        case 'fr':
         $listout .= htmlspecialchars(stripslashes($sbp ['page_title_fr']));
         break;
        case 'ru':
         $listout .= htmlspecialchars(stripslashes($sbp ['page_title_ru']));
         break;
        default:
         $listout .= htmlspecialchars(stripslashes($sbp ['page_title'.$GLOBALS['site']->getLangDefKod()]));
         break;
        }
     $listout .= '</a></h4>
       <p>';
    switch ($_COOKIE ['lang']) {
        case 'hu':
         $listout .= nl2br(htmlspecialchars(stripslashes($sbp ['page_eleir'])));
         break;
        case 'de':
         $listout .= nl2br(htmlspecialchars(stripslashes($sbp ['page_eleir_de'])));
         break;
        case 'en':
         $listout .= nl2br(htmlspecialchars(stripslashes($sbp ['page_eleir_en'])));
         break;
        case 'fr':
         $listout .= nl2br(htmlspecialchars(stripslashes($sbp ['page_eleir_fr'])));
         break;
        case 'ru':
         $listout .= nl2br(htmlspecialchars(stripslashes($sbp ['page_eleir_ru'])));
         break;
        default:
         $listout .= nl2br(htmlspecialchars(stripslashes($sbp ['page_eleir'.$GLOBALS['site']->getLangDefKod()])));
         break;
        }
    $listout .= '</p>
       <a href="aloldal/'.$sbp ['page_slug'].'/" class="c777">'.$GLOBALS['_blokk_2_1'].'</a>
      </div>
     </div>';
      }
   $mysql->close;
   return $listout;
   }
  /*getters*/
  public function getKeyw() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return htmlspecialchars(stripslashes($this->pagekeyw));
     break;
    case 'de':
     return htmlspecialchars(stripslashes($this->pagekeyw_de));
     break;
    case 'en':
     return htmlspecialchars(stripslashes($this->pagekeyw_en));
     break;
    case 'fr':
     return htmlspecialchars(stripslashes($this->pagekeyw_fr));
     break;
    case 'ru':
     return htmlspecialchars(stripslashes($this->pagekeyw_ru));
     break;
    default:
     return htmlspecialchars(stripslashes($this->pagekeyw_def));
     break;
   }
  }
  public function getDesc() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return htmlspecialchars(stripslashes($this->pagedesc));
     break;
    case 'de':
     return htmlspecialchars(stripslashes($this->pagedesc_de));
     break;
    case 'en':
     return htmlspecialchars(stripslashes($this->pagedesc_en));
     break;
    case 'fr':
     return htmlspecialchars(stripslashes($this->pagedesc_fr));
     break;
    case 'ru':
     return htmlspecialchars(stripslashes($this->pagedesc_ru));
     break;
    default:
     return htmlspecialchars(stripslashes($this->pagedesc_def));
     break;
   }
  }
  public function getTitle() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return htmlspecialchars(stripslashes($this->pagetitle));
     break;
    case 'de':
     return htmlspecialchars(stripslashes($this->pagetitle_de));
     break;
    case 'en':
     return htmlspecialchars(stripslashes($this->pagetitle_en));
     break;
    case 'fr':
     return htmlspecialchars(stripslashes($this->pagetitle_fr));
     break;
    case 'ru':
     return htmlspecialchars(stripslashes($this->pagetitle_ru));
     break;
    default:
     return htmlspecialchars(stripslashes($this->pagetitle_def));
     break;
   }
  }
  /*public function getText() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->pagetext);
     break;
    case 'de':
     return stripslashes($this->pagetext_de);
     break;
    case 'en':
     return stripslashes($this->pagetext_en);
     break;
    case 'fr':
     return stripslashes($this->pagetext_fr);
     break;
    case 'ru':
     return stripslashes($this->pagetext_ru);
     break;
    default:
     return stripslashes($this->pagetext_def);
     break;
   }
  }*/
  
  public function getText() {   
	 switch ($_COOKIE ['lang']) {
    case 'hu':
     $txt = stripslashes($this->pagetext);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    case 'de':
     //return stripslashes($this->pagetext_de);
     $txt = stripslashes($this->pagetext_de);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    case 'en':
     //return stripslashes($this->pagetext_en);
     $txt = stripslashes($this->pagetext_en);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    case 'fr':
     //return stripslashes($this->pagetext_fr);
     $txt = stripslashes($this->pagetext_fr);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    case 'ru':
     //return stripslashes($this->pagetext_ru);
     $txt = stripslashes($this->pagetext_ru);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
    default:
     //return stripslashes($this->pagetext);
     $txt = stripslashes($this->pagetext_def);
     $texthossz = strlen($txt)/2;
     $firsthalf = substr($txt, 0, $texthossz);
     
     $pos [] = strrpos($firsthalf, '.');
     $pos [] = strrpos($firsthalf, ',');
     $pos [] = strrpos($firsthalf, '!');
     $pos [] = strrpos($firsthalf, '?');
     $pos [] = strrpos($firsthalf, '>');
     $lastpos = max($pos);
     $half = substr($txt, 0, $lastpos+1);
     $halfhird = $half.'[hird]';
     $txt = str_replace($half, $halfhird, $txt);
     $hrd = gethirdetes ('tartalom');
     $txt = str_replace('[hird]', $hrd, $txt);
		 return $txt;
     break;
   }
  }
  
  public function getAnchor() {
   return $this->anchor;
  }
  /*public function getExtrameta() {
   return stripslashes($this->extrameta);
  }*/
  public function getSlug() {
   return stripslashes($this->slug);
  }
}
?>
