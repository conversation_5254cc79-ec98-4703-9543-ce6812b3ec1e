<?php
class Hir {
  
  private $prefix;
  private $slug;
  private $nev;
  private $nev_en;
  private $nev_pl;
  private $elozetes;
  private $elozetes_en;
  private $elozetes_pl;
  private $szoveg;
  private $szoveg_en;
  private $szoveg_pl;
  private $keyw;
  private $keyw_en;
  private $keyw_pl;
  private $desc;
  private $desc_en;
  private $desc_pl;
  private $kep;
  private $aktiv;
  private $gal;
  private $mail;
  private $date;
  
    
  function __construct($slug, $prefix = '') {
   $this->prefix = $prefix;
   $this->slug = $slug;
  }
  
  public function getData() {
   $mysql = connecti();
   $sql_cdata = "Select * From ".$this->prefix."hirek Where hirek_slug = '$this->slug'";
     
   $ret_cdata = $mysql->query ($sql_cdata);
   while ( $pgs = $ret_cdata->fetch_array (MYSQLI_ASSOC) ) {
      $this->nev = $pgs ['hirek_nev'];
      $this->nev_en = $pgs ['hirek_nev_en'];
      $this->nev_pl = $pgs ['hirek_nev_pl'];
      $this->elozetes = $pgs ['hirek_elozetes'];
      $this->elozetes_en = $pgs ['hirek_elozetes_en'];
      $this->elozetes_pl = $pgs ['hirek_elozetes_pl'];
      $this->szoveg = $pgs ['hirek_szoveg'];
      $this->szoveg_en = $pgs ['hirek_szoveg_en'];
      $this->szoveg_pl = $pgs ['hirek_szoveg_pl'];
      $this->keyw = $pgs ['hirek_keyw'];
      $this->keyw_en = $pgs ['hirek_keyw_en'];
      $this->keyw_pl = $pgs ['hirek_keyw_pl'];
      $this->desc = $pgs ['hirek_desc'];
      $this->desc_en = $pgs ['hirek_desc_en'];
      $this->desc_pl = $pgs ['hirek_desc_pl'];
      $this->kep = $pgs ['hirek_kep'];
      $this->aktiv = $pgs ['hirek_aktiv'];
      $this->gal = $pgs ['hirek_gal'];
      $this->date = $pgs ['hirek_letrehozas'];
   }
  }
	   
  /*getters*/
  public function getKeyw() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->keyw);
     break;
    case 'en':
     return stripslashes($this->keyw_en);
     break;
    case 'pl':
     return stripslashes($this->keyw_pl);
     break;
    default:
     return stripslashes($this->keyw);
     break;
   }
  }
  
  public function getDesc() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->desc);
     break;
    case 'en':
     return stripslashes($this->desc_en);
     break;
    case 'pl':
     return stripslashes($this->desc_pl);
     break;
    default:
     return stripslashes($this->desc);
     break;
   }
  }
  
  public function getTitle() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->nev);
     break;
    case 'en':
     return stripslashes($this->nev_en);
     break;
    default:
     return stripslashes($this->nev);
     break;
   }
  }
  
  public function getText() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->szoveg);
     break;
    case 'en':
     return stripslashes($this->szoveg_en);
     break;
    case 'pl':
     return stripslashes($this->szoveg_pl);
     break;
    default:
     return stripslashes($this->szoveg);
     break;
   }
  }
  
  public function getElozetes() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->elozetes);
     break;
    case 'en':
     return stripslashes($this->elozetes_en);
     break;
    case 'pl':
     return stripslashes($this->elozetes_pl);
     break;
    default:
     return stripslashes($this->elozetes);
     break;
   }
  }
  
  public function getSlug() {
   return stripslashes($this->slug);
  }
  
  public function getKep() {
   if ( $this->kep != '' ) {
    $retkep = '<img src="content/hirkep/'.$this->kep.'" alt="'.$this->getTitle().'" title="'.$this->getTitle().'" />';
   } else {
    $retkep = '<img src="img/nincs_kep.jpg" alt="'.$this->getTitle().'" title="'.$this->getTitle().'" />';
   }
   return $retkep;
  }
  
  public function getTnKep() {
   if ( $this->kep != '' ) {
    $retkep = '<a style="display: block; float: left; margin: 20px;" href="content/hirkep/'.$this->kep.'"><img src="content/hirkep/tn_'.$this->kep.'" alt="'.$this->getTitle().'" title="'.$this->getTitle().'" /></a>';
   } else {
    $retkep = '<a style="display: block; float: left; margin: 20px;" href="/hir-olvas/'.$this->slug.'"><img src="img/tn_nincs_kep.jpg" alt="'.$this->getTitle().'" title="'.$this->getTitle().'" height="270" width="162" /></a>';
   }
   return $retkep;
  }
  
  public function getKepLink() {
   if ( $this->kep != '' ) {
    $retkep = $site_url.'content/hirkep/'.$this->kep;
   } else {
    $retkep = $site_url.'img/nincs_kep.jpg';
   }
   return $retkep;
  }
  
  public function getDate() {
   return $this->date;
  }
  
  public function getSocials() {
   print '
    <br />
    <span style="font-size: 1.2em;">
    <a href="https://www.facebook.com/sharer/sharer.php?u='.$site_url.'hir-olvas/'.$this->slug.'" target="_blank"><i class="fa fa-facebook-official" aria-hidden="true"></i></a>
	<a href="https://twitter.com/home?status='.$site_url.'hir-olvas/'.$this->slug.'" target="_blank"><i class="fa fa-twitter" aria-hidden="true"></i></a>
    </span>';
  }
  
  public function getGal() {
   print '<hr />
   <div class="row">';
   $mysql = connecti();
   $sql_selgal = "Select * From galeria_kep Where galeria_kep_galeria = '$this->gal' Order By galeria_kep_id ASC";
   $ret_selgal = $mysql->query ($sql_selgal);
   while ( $sg = $ret_selgal->fetch_array (MYSQLI_ASSOC) ) {
    print '<div class="col-xs-6 col-sm-6 col-lg-3">';
    print '<a href="'.$site_url.'content/gal/'.$sg ['galeria_kep_kep'].'" class="lightbox"><img src="'.$site_url.'content/gal/tn_'.$sg ['galeria_kep_kep'].'" class="img-responsive" alt="'.$this->getTitle().'" title="'.$this->getTitle().'" /></a>
    </div>';
   }
   print '</div>';
  }
  
}

?>
