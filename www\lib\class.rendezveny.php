<?php
class Rendezveny {
  
  private $prefix;
  private $slug;
  private $nev;
  private $nev_en;
  private $nev_pl;
  private $elozetes;
  private $elozetes_en;
  private $elozetes_pl;
  private $szoveg;
  private $szoveg_en;
  private $szoveg_pl;
  private $keyw;
  private $keyw_en;
  private $keyw_pl;
  private $desc;
  private $desc_en;
  private $desc_pl;
  private $kep;
  private $aktiv;
  private $gal;
  private $mail;
  private $date;
  
    
  function __construct($slug, $prefix = '') {
   $this->prefix = $prefix;
   $this->slug = $slug;
  }
  
  public function getData() {
   $mysql = connecti();
   $sql_cdata = "Select * From ".$this->prefix."programok Where programok_slug = '$this->slug'";
     
   $ret_cdata = $mysql->query ($sql_cdata);
   while ( $pgs = $ret_cdata->fetch_array (MYSQLI_ASSOC) ) {
      $this->nev = $pgs ['programok_nev'];
      $this->nev_en = $pgs ['programok_nev_en'];
      $this->nev_pl = $pgs ['programok_nev_pl'];
      $this->elozetes = $pgs ['programok_elozetes'];
      $this->elozetes_en = $pgs ['programok_elozetes_en'];
      $this->elozetes_pl = $pgs ['programok_elozetes_pl'];
      $this->szoveg = $pgs ['programok_szoveg'];
      $this->szoveg_en = $pgs ['programok_szoveg_en'];
      $this->szoveg_pl = $pgs ['programok_szoveg_pl'];
      $this->keyw = $pgs ['programok_keyw'];
      $this->keyw_en = $pgs ['programok_keyw_en'];
      $this->keyw_pl = $pgs ['programok_keyw_pl'];
      $this->desc = $pgs ['programok_desc'];
      $this->desc_en = $pgs ['programok_desc_en'];
      $this->desc_pl = $pgs ['programok_desc_pl'];
      $this->kep = $pgs ['programok_kep'];
      $this->aktiv = $pgs ['programok_aktiv'];
      $this->mail = $pgs ['programok_mail'];
      $this->gal = $pgs ['programok_gal'];
      $this->date = $pgs ['programok_date'];
   }
  }
	   
  /*getters*/
  public function getKeyw() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->keyw);
     break;
    case 'en':
     return stripslashes($this->keyw_en);
     break;
    case 'pl':
     return stripslashes($this->keyw_pl);
     break;
    default:
     return stripslashes($this->keyw);
     break;
   }
  }
  
  public function getDesc() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->desc);
     break;
    case 'en':
     return stripslashes($this->desc_en);
     break;
    case 'pl':
     return stripslashes($this->desc_pl);
     break;
    default:
     return stripslashes($this->desc);
     break;
   }
  }
  
  public function getNev() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->nev);
     break;
    case 'en':
     return stripslashes($this->nev_en);
     break;
    case 'pl':
     return stripslashes($this->nev_fr);
     break;
    default:
     return stripslashes($this->nev);
     break;
   }
  }
  
  public function getText() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->szoveg);
     break;
    case 'en':
     return stripslashes($this->szoveg_en);
     break;
    case 'pl':
     return stripslashes($this->szoveg_pl);
     break;
    default:
     return stripslashes($this->szoveg);
     break;
   }
  }
  
  public function getElozetes() {
   switch ($_COOKIE ['lang']) {
    case 'hu':
     return stripslashes($this->elozetes);
     break;
    case 'en':
     return stripslashes($this->elozetes_en);
     break;
    case 'pl':
     return stripslashes($this->elozetes_pl);
     break;
    default:
     return stripslashes($this->elozetes);
     break;
   }
  }
  
  public function getSlug() {
   return stripslashes($this->slug);
  }
  
  public function getKep() {
   if ( $this->kep != '' ) {
    $retkep = '<img src="content/prog/'.$this->kep.'" alt="'.$this->getNev().'" title="'.$this->getNev().'" />';
   } else {
    $retkep = '<img src="img/nincs_kep.jpg" alt="'.$this->getNev().'" title="'.$this->getNev().'" />';
   }
   return $retkep;
  }
  
  public function getTnKep() {
   if ( $this->kep != '' ) {
    $retkep = '<a href="content/prog/'.$this->kep.'" class="lightbox"><img src="content/civil/tn_'.$this->kep.'" alt="'.$this->getNev().'" title="'.$this->getNev().'" class="img-responsive" /></a>';
   } else {
    $retkep = '<a href="img/nincs_kep.jpg" class="lightbox"><img src="img/tn_nincs_kep.jpg" alt="'.$this->getNev().'" title="'.$this->getNev().'" class="img-responsive" /></a>';
   }
   return $retkep;
  }
  
  public function getKepLink() {
   if ( $this->kep != '' ) {
    $retkep = $site_url.'content/prog/'.$this->kep;
   } else {
    $retkep = $site_url.'img/nincs_kep.jpg';
   }
   return $retkep;
  }
  
  public function getDate() {
   return $this->date;
  }
  
  public function getKapcsForm() {
   $kpcs = '<hr />
      <form action="/szallas-kapcsolat" method="post">
       <strong>Kapcsolat felvétel</strong><br /><br />
       <input type="hidden" name="slug" value="'.$this->slug.'">
       
       <div class="form-group">
        <label class="control-label col-md-3 col-sm-12 col-xs-12">Az Ön neve</label>
        <div class="col-md-9 col-sm-12 col-xs-12">
         <input type="text" class="form-control" name="nev" required>
        </div>
       </div>
       <br /><br />
       <div class="form-group">
        <label class="control-label col-md-3 col-sm-12 col-xs-12">Az Ön e-mail címe</label>
        <div class="col-md-9 col-sm-12 col-xs-12">
         <input type="email" class="form-control" name="email" required>
        </div>
       </div>
       <br /><br />
       <div class="form-group">
        <label class="control-label col-md-3 col-sm-12 col-xs-12">Üzenet</label>
        <div class="col-md-9 col-sm-12 col-xs-12">
         <textarea name="uzenet" rows="3" class="form-control"></textarea>
        </div>
       </div>
       
       <div class="form-group">
        <div class="col-md-12 col-sm-12 col-xs-12">
         <button type="submit" class="btn btn-primary pull-right">Üzenet küldése</button>
        </div>
       </div>
       
      </form>';
   return $kpcs;
  }
  
  public function getSocials() {
   print '
    <br />
    <span style="font-size: 1.2em;">
    <a href="https://www.facebook.com/sharer/sharer.php?u='.$site_url.'program/'.$this->slug.'" target="_blank"><i class="fa fa-facebook-official" aria-hidden="true"></i></a>
	<a href="https://twitter.com/home?status='.$site_url.'program/'.$this->slug.'" target="_blank"><i class="fa fa-twitter" aria-hidden="true"></i></a>
    </span>';
  }
  
  public function getGal() {
   print '<hr />
   <div class="row">';
   $mysql = connecti();
   $sql_selgal = "Select * From galeria_kep Where galeria_kep_galeria = '$this->gal' Order By galeria_kep_id ASC";
   $ret_selgal = $mysql->query ($sql_selgal);
   while ( $sg = $ret_selgal->fetch_array (MYSQLI_ASSOC) ) {
    print '<div class="col-xs-6 col-sm-6 col-lg-3">';
    print '<a href="'.$site_url.'content/gal/'.$sg ['galeria_kep_kep'].'" class="lightbox"><img src="'.$site_url.'content/gal/tn_'.$sg ['galeria_kep_kep'].'" class="img-responsive" alt="'.$this->getNev().'" title="'.$this->getNev().'" /></a>
    </div>';
   }
   print '</div>';
  }
  
}

?>
