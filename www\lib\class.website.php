<?php
class WebSite {
	
	private $site_domain;
	private $site_name;
	private $site_css;
	private $site_blind_css;
	private $site_lang;
	private $site_lang_en;
	private $site_lang_de;
	private $site_lang_fr;
	private $site_lang_ru;
	private $site_lang_default;
	private $site_id;
	private $site_head_seo;
	private $site_foot_seo;
	private $chat_aktiv;
	private $valtomenu;
	private $huaktiv;
	private $enaktiv;
	private $deaktiv;
	private $fraktiv;
	private $ruaktiv;
	private $prefix;
	private $blokk_1;
	private $blokk_2;
	private $blokk_3;
	private $blokk_4;
	private $blokk_5;
	private $blokk_6;
	private $blokk_galeria;
	private $blokk_fiok;
	private $blokk_9_fejlesztes;
	private $blokk_10_hirek;
	private $blokk_11_shop;
	private $blokk_12_hirlev;
	private $blokk_13_vengedvasar;
	private $blokk_14;
	private $blokk_15;
	private $blokk_16;
	private $blokk_17;
	private $blokk_18;
  private $blokk_19; //upsell
  public $infoline;

  public $barion_poskey;
  public $barion_pubkey;
  public $barion_mail;
  public $barion_prod;
  public $szamlazzhu_apikey;
  public $szamlazzhu_mail;
  public $szamlazzhu_szoftver;
  public $ingyenszallitas_kosarertek;
  public $paypal_account;
  public $paypal_clientid;
  public $paypal_secret;
  public $paypal_prod;
  public $captcha_public;
  public $captcha_secret;
  public $afakod;

  public $mailServer;
  public $mailUser;
  public $mailPass;
  
  function __construct($domain, $prefix) {
  	$mysql = connecti();
  	$this->prefix = $prefix;
  	$rqdomain = str_replace('www.', '', $domain);
  	$sql_getsite = "Select * From ".$this->prefix."domains Where domains_domain = '$rqdomain' Limit 1";
   //print $sql_getsite;
  	$ret_getsite = $mysql->query ($sql_getsite);
  	while ( $gt = $ret_getsite->fetch_array (MYSQLI_ASSOC) ) {
  		$this->site_domain = $gt ['domains_domain'];
	  //$this->site_name = $gt ['domains_sitename'];
  		$this->site_css = $gt ['domains_css'];
  		$this->site_blind_css = $gt ['domains_blind_css'];
  		$this->site_lang = $gt ['domains_lang'];
  		$this->site_lang_en = $gt ['domains_lang_en'];
  		$this->site_lang_de = $gt ['domains_lang_de'];
  		$this->site_lang_fr = $gt ['domains_lang_fr'];
  		$this->site_lang_ru = $gt ['domains_lang_ru'];
  		
  		switch ($gt ['domains_default_lang']) {
  			case 'hu':
  			$this->site_lang_default = $this->site_lang;
  			break;
  			case 'de':
  			$this->site_lang_default = $this->site_lang_de;
  			break;
  			case 'en':
  			$this->site_lang_default = $this->site_lang_en;
  			break;
  			case 'fr':
  			$this->site_lang_default = $this->site_lang_fr;
  			break;
  			case 'ru':
  			$this->site_lang_default = $this->site_lang_ru;
  			break;
  			default:
  			$this->site_lang_default = $this->site_lang;
  			break;
  		}
  		
  		$this->site_id = $gt ['domains_id'];
  		$this->site_head_seo = $gt ['domains_seo_header'];
  		$this->site_foot_seo = $gt ['domains_seo_footer'];
  		$this->chat_aktiv = $gt ['domains_chat_aktiv'];
  		$this->valtomenu = $gt ['domains_lang_menu'];
  		$this->huaktiv = $gt ['domains_lang_hu_aktiv'];
  		$this->enaktiv = $gt ['domains_lang_en_aktiv'];
  		$this->deaktiv = $gt ['domains_lang_de_aktiv'];
  		$this->fraktiv = $gt ['domains_lang_fr_aktiv'];
  		$this->ruaktiv = $gt ['domains_lang_ru_aktiv'];
  		$this->infoline = $gt ['domains_infocsik'];
	  //print $gt ['domains_id'];
  		
  		$sql_selblokkok = "Select * From ".$this->prefix."blokkok Where blokkok_id = '1'";
  		$ret_selblokkok = $mysql->query ($sql_selblokkok);
  		while ( $seb = $ret_selblokkok->fetch_array (MYSQLI_ASSOC) ) {
  			$this->blokk_1 = $seb ['blokkok_1'];
  			$this->blokk_2 = $seb ['blokkok_2'];
  			$this->blokk_3 = $seb ['blokkok_3'];
  			$this->blokk_4 = $seb ['blokkok_4'];
  			$this->blokk_5 = $seb ['blokkok_5'];
  			$this->blokk_6 = $seb ['blokkok_6'];
  			$this->blokk_galeria = $seb ['blokkok_7'];
  			$this->blokk_fiok = $seb ['blokkok_8'];
  			$this->blokk_9_fejlesztes = $seb ['blokkok_9'];
  			$this->blokk_10_hirek = $seb ['blokkok_10'];
  			$this->blokk_11_shop = $seb ['blokkok_11'];
  			$this->blokk_12_hirlev = $seb ['blokkok_12'];
  			$this->blokk_13_vengedvasar = $seb ['blokkok_13'];
  			$this->blokk_14 = $seb ['blokkok_14'];
  			$this->blokk_15 = $seb ['blokkok_15'];
  			$this->blokk_16 = $seb ['blokkok_16'];
  			$this->blokk_17 = $seb ['blokkok_17'];
  			$this->blokk_18 = $seb ['blokkok_18'];
  			$this->blokk_19 = $seb ['blokkok_19'];
  		}
  	}

  	$sql_selszfiz = "Select * From ".$this->prefix."fizetesszamlazas Where fizetesszamlazas_id = 1";
  	$ret_selszfiz = $mysql->query ($sql_selszfiz);
  	while ($f = $ret_selszfiz->fetch_array (MYSQLI_ASSOC)) {
  		$this->barion_poskey = $f ['fizetesszamlazas_barion_poskey'];
  		$this->barion_pubkey = $f ['fizetesszamlazas_barion_publickey'];
  		$this->barion_mail = $f ['fizetesszamlazas_barion_mail'];
  		$this->barion_prod = $f ['fizetesszamlazas_barion_prod'];
  		$this->szamlazzhu_apikey = $f ['fizetesszamlazas_szamlazzhu_apikey'];
  		$this->szamlazzhu_mail = $f ['fizetesszamlazas_szamlazzhu_mail'];
  		$this->szamlazzhu_szoftver = $f ['fizetesszamlazas_szamlazzhu_szamlazoszoftver'];
  		$this->ingyenszallitas_kosarertek = $f ['fizetesszamlazas_ingyenes_kosarertek'];
  		$this->paypal_account = $f ['fizetesszamlazas_paypal_account'];
  		$this->paypal_clientid = $f ['fizetesszamlazas_paypal_clientid'];
  		$this->paypal_secret = $f ['fizetesszamlazas_paypal_secret'];
  		$this->paypal_prod = $f ['fizetesszamlazas_paypal_prod'];
  		$this->captcha_public = $f ['fizetesszamlazas_captcha_public'];
  		$this->captcha_secret = $f ['fizetesszamlazas_captcha_secret'];
  		$this->afakod = $f ['fizetesszamlazas_afakod'];
  		$this->mailServer = $f ['fizetesszamlazas_mailserver'];
  		$this->mailUser = $f ['fizetesszamlazas_mailuser'];
  		$this->mailPass = $f ['fizetesszamlazas_mailpass'];
  	}
  	$mysql->close;
  }
  
  
  public function getDomain() {
  	return $this->site_domain;
  }
  
  public function getSiteName() {
  	return $this->site_name;
  }
  
  public function getCss() {
  	if ( isset($_COOKIE ['blind']) ) {
  		$retcss = $this->site_blind_css;
  	} else {
  		$retcss = $this->site_css;
  	}
  	return $retcss;
  }
  
  public function getLang() {
  	$retlang = $this->site_lang;
  	return $retlang;
  }
  
  public function getLangEn() {
  	$retlang = $this->site_lang_en;
  	return $retlang;
  }
  
  public function getLangDe() {
  	$retlang = $this->site_lang_de;
  	return $retlang;
  }

  public function getLangFr() {
  	$retlang = $this->site_lang_fr;
  	return $retlang;
  }
  
  public function getLangRu() {
  	$retlang = $this->site_lang_ru;
  	return $retlang;
  }
  
  public function getLangDef() {
  	$retlang = $this->site_lang_default;
  	return $retlang;
  }

  public function getLangDefKod() {
  	$lng = $this->site_lang_default;
  	$lng = str_replace('content/lang/lang_', '', $lng);
	$lng = str_replace('content/lang/lang', '', $lng);
  	$lng = str_replace('.php', '', $lng);
  	if ( $lng == 'hu' or $lng == '' ) {
  		$lng = '';
  	} else {
  		$lng = '_'.$lng;
  	}
  	return $lng;
  }
  
  public function getLangMenu() {
  	return $this->valtomenu;
  }
  
  public function getLangHuAktiv() {
  	return $this->huaktiv;
  }
  
  public function getLangEnAktiv() {
  	return $this->enaktiv;
  }
  
  public function getLangDeAktiv() {
  	return $this->deaktiv;
  }
  
  public function getLangFrAktiv() {
  	return $this->fraktiv;
  }
  
  public function getLangRuAktiv() {
  	return $this->ruaktiv;
  }
  
  public function getBlokk1() {
  	return $this->blokk_1;
  }
  
  public function getBlokk2() {
  	return $this->blokk_2;
  }
  
  public function getBlokk3() {
  	return $this->blokk_3;
  }
  
  public function getBlokk4() {
  	return $this->blokk_4;
  }
  
  public function getBlokk5() {
  	return $this->blokk_5;
  }
  
  public function getBlokk6() {
  	return $this->blokk_6;
  }
  
  public function getBlokkGaleria() {
  	return $this->blokk_galeria;
  }
  
  public function getBlokkFiok() {
  	return $this->blokk_fiok;
  }

  public function getBlokk9Atalak() {
  	return $this->blokk_9_fejlesztes;
  }
  
  public function getBlokk10() {
  	return $this->blokk_10_hirek;
  }
  
  public function getBlokk11() {
  	return $this->blokk_11_shop;
  }
  
  public function getBlokk12() {
  	return $this->blokk_12_hirlev;
  }

  public function getBlokk13() {
  	return $this->blokk_13_vengedvasar;
  }

  public function getBlokk14() { //akviós termék
  	return $this->blokk_14;
  }

	public function getBlokk15() { //legújabb termék
		return $this->blokk_15;
	}

	public function getBlokk16() { //kategóriák blokk
		return $this->blokk_16;
	}

	public function getBlokk17() { //hasonló termék
		return $this->blokk_17;
	}

	public function getBlokk18() { //popup hírlevél feliratkozás
		return $this->blokk_18;
	}

	public function getBlokk19() { //Upsell
		return $this->blokk_19;
	}
	
	public function getSiteId() {
		return $this->site_id;
	}
	
	public function getHeadSeo() {
		return $this->site_head_seo;
	}
	
	public function getFootSeo() {
		return $this->site_foot_seo;
	}
	
	public function getChat() {
		if ( $this->chat_aktiv == 0 ) {
			return '';
		} else {	  
			$cht = '<!-- chat szekcio -->
			<div id="chat_main">
			<div class="chathead" onclick="openChat();clearTimeout(to);">
			'.$GLOBALS ['_chat_1'].'<i class="fa fa-comments-o" aria-hidden="true"></i>
			</div>
			<div class="chatcont">
			<div class="chattitle">
			<div class="l-p">'.$GLOBALS ['_chat_10'].'</div>
			<div class="r-p"><a href="#" onclick="openChat();clearTimeout(to);return false;" class="close-chat"><i class="fa fa-minus"></i></a></div>
			</div>
			<div class="chatpadding" id="chatpadding">
			<div id="chatresponse"></div>';
			$cht .= '<div class="message">';
			$cht .= '<input type="text" id="pst" class="form-control" placeholder="'.$GLOBALS ['_chat_4'].'" onkeypress="if(event.keyCode == 13){postChat();}">
			<button class="btn btn-info" type="button" id="chbtn" onclick="postChat();">'.$GLOBALS ['_chat_5'].'</button>
			<!--<div class="text-center"><a href="#blokk-6" onclick="openChat();clearTimeout(to);return false;">'.$GLOBALS ['_chat_6'].'</a> | <a href="'.$GLOBALS ['_suti_4'].'" target="_blank">'.$GLOBALS ['_chat_7'].'</a></div>-->
			<div class="text-center"><a href="'.$GLOBALS['site_url'].'kapcsolat/" onclick="openChat();clearTimeout(to);">'.$GLOBALS ['_chat_6'].'</a> | <a href="'.$GLOBALS ['_suti_4'].'" target="_blank">'.$GLOBALS ['_chat_7'].'</a></div>
			</div>
			</div>';

			
//$cht .= '<div id="chatclose" style="display:none;">'.$GLOBALS ['_chat_9'].'<br /><br /><a href="#blokk-6" onclick="openChat();clearTimeout(to);return false;">'.$GLOBALS ['_chat_6'].'</a></div>';
			$cht .= '<div id="chatclose" style="display:none;">'.$GLOBALS ['_chat_9'].'<br /><br /><a href="'.$GLOBALS['site_url'].'kapcsolat/" onclick="openChat();clearTimeout(to);">'.$GLOBALS ['_chat_6'].'</a></div>';
			$cht .= '</div>
			</div>
			';


			return $cht;
		}
		
	}
	
}
?>