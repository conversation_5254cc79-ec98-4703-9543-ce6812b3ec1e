<?php
class Termek {
	public $cikkszam;
	public $azon;
	public $mainkep;
	public $kepek;
	public $kisKeplista;
	public $title;
	public $keyw;
	public $desc;
	public $termleir;
	public $title_en;
	public $keyw_en;
	public $desc_en;
	public $termleir_en;
	public $title_de;
	public $keyw_de;
	public $desc_de;
	public $termleir_de;
	public $title_fr;
	public $keyw_fr;
	public $desc_fr;
	public $termleir_fr;
	public $title_ru;
	public $keyw_ru;
	public $desc_ru;
	public $termleir_ru;
	public $title_def;
	public $keyw_def;
	public $desc_def;
	public $termleir_def;
	public $kategoria;
	public $multiKategoria = array();
	public $szeles;
	public $magas;
	public $hosszu;
	public $mennyiseg;
	public $opcioMennyiseg = -1;
	public $brutto;
	public $netto;
	public $akcmertek;
	public $akcbrutto;
	public $akcnetto;
	public $szallido;
	public $microdata;
	public $slug;
	public $aktiv;
	public $prefix;
	public $opcioszam = 0;
	public $mennyisegesOpcioszam = -1;

	function __construct($prefix) {
		$prms = explode("/", $_GET ['params']);
		$prms = array_filter($prms);
		$slug = $prms [0];
		$this->slug = $slug;
		$this->prefix = $prefix;
	}

	public function getPageData() {
		$mysql = connecti();
		if ( !is_numeric($this->slug) ) {
			$sql_gettermek = "Select * From ".$this->prefix."termek Where termek_slug = '".$this->slug."'";
		} else {
			$sql_gettermek = "Select * From ".$this->prefix."termek Where termek_id = '".$this->slug."'";
		}
		$ret_gettermek = $mysql->query ($sql_gettermek);
		while ( $pgs = $ret_gettermek->fetch_array (MYSQLI_ASSOC) ) {
			$this->cikkszam = htmlspecialchars(stripslashes($pgs ['termek_cikkszam']));
			$this->keyw = htmlspecialchars(stripslashes($pgs ['termek_keyw']));
			$this->desc = htmlspecialchars(stripslashes($pgs ['termek_desc']));
			$this->title = htmlspecialchars(stripslashes($pgs ['termek_nev']));
			$this->termleir = stripslashes($pgs ['termek_leiras']);
			$this->keyw_en = htmlspecialchars(stripslashes($pgs ['termek_keyw_en']));
			$this->desc_en = htmlspecialchars(stripslashes($pgs ['termek_desc_en']));
			$this->title_en = htmlspecialchars(stripslashes($pgs ['termek_nev_en']));
			$this->termleir_en = stripslashes($pgs ['termek_leiras_en']);
			$this->keyw_de = htmlspecialchars(stripslashes($pgs ['termek_keyw_de']));
			$this->desc_de = htmlspecialchars(stripslashes($pgs ['termek_desc_de']));
			$this->title_de = htmlspecialchars(stripslashes($pgs ['termek_nev_de']));
			$this->termleir_de = stripslashes($pgs ['termek_leiras_de']);
			$this->keyw_fr = htmlspecialchars(stripslashes($pgs ['termek_keyw_fr']));
			$this->desc_fr = htmlspecialchars(stripslashes($pgs ['termek_desc_fr']));
			$this->title_fr = htmlspecialchars(stripslashes($pgs ['termek_nev_fr']));
			$this->termleir_fr = stripslashes($pgs ['termek_leiras_fr']);
			$this->keyw_ru = htmlspecialchars(stripslashes($pgs ['termek_keyw_ru']));
			$this->desc_ru = htmlspecialchars(stripslashes($pgs ['termek_desc_ru']));
			$this->title_ru = htmlspecialchars(stripslashes($pgs ['termek_nev_ru']));
			$this->termleir_ru = stripslashes($pgs ['termek_leiras_ru']);
			$this->title_def = htmlspecialchars(stripslashes($pgs ['termek_nev'.$GLOBALS['site']->getLangDefKod()]));
			$this->keyw_def = htmlspecialchars(stripslashes($pgs ['termek_keyw'.$GLOBALS['site']->getLangDefKod()]));
			$this->desc_def = htmlspecialchars(stripslashes($pgs ['termek_desc'.$GLOBALS['site']->getLangDefKod()]));
			$this->termleir_def = stripslashes($pgs ['termek_leiras'.$GLOBALS['site']->getLangDefKod()]);

			$this->slug = htmlspecialchars(stripslashes($pgs ['termek_slug']));
			$this->aktiv = htmlspecialchars(stripslashes($pgs ['termek_aktiv']));
			$this->azon = $pgs ['termek_id'];
			$this->kepek = $pgs ['termek_kep'];
			$this->brutto = $pgs ['termek_brutto'];
			$this->akcmertek = $pgs ['termek_akcio_mertek'];
			$this->akcbrutto = $pgs ['termek_akcbrutto'];
			$this->bombbrutto = $pgs ['termek_bombbrutto'];
			$this->kategoria = htmlspecialchars(stripslashes($pgs ['termek_kategoria_id']));
			$tpkats = explode('[', $this->kategoria);
			foreach ($tpkats as $tk => $tv) {
				$tv = str_replace('[', '', $tv);
				$tv = str_replace(']', '', $tv);
				if ( $tv != '' ) {
					$this->$multiKategoria [] = $tv;
				}
			}

			$this->szeles = $pgs ['termek_szelesseg'];
			$this->magas = $pgs ['termek_magassag'];
			$this->hosszu = $pgs ['termek_hosszusag'];
			$this->mennyiseg = $pgs ['termek_mennyiseg'];
			$this->szallido = $pgs ['termek_szallido'];
			$kparr = array();
			$sql_selkepek = "Select * From ".$this->prefix."termek_kep Where termek_kep_termek = '$this->azon' Order by termek_kep_sorrend ASC";
			$ret_selkepek = $mysql->query($sql_selkepek);
			$mk = 0;
			while ( $skp = $ret_selkepek->fetch_array(MYSQLI_ASSOC) ) {
				if ( $mk == 0 ) {
					$mk = 1;
					$this->mainkep = $skp ['termek_kep_kep'];
				} else {
					$kparr [] = $skp ['termek_kep_kep'];
				}
			}
			$this->kisKeplista = $kparr;
		}

		$sql_selopciok = "Select * From ".$this->prefix."termekopcio_mennyiseg Where termekopcio_mennyiseg_termek = '$this->azon'";
		$ret_selopciok = $mysql->query($sql_selopciok);
		$this->opcioszam = $ret_selopciok->num_rows;
		if ( $this->opcioszam > 0 ) {
			while ( $on = $ret_selopciok->fetch_array(MYSQLI_ASSOC) ) {
				if ( $on ['termekopcio_mennyiseg_keszlet'] >= 0 ) {
					if ( $this->mennyisegesOpcioszam < 0 ) {
						$this->mennyisegesOpcioszam = 0;
					}
					$this->mennyisegesOpcioszam += $on ['termekopcio_mennyiseg_keszlet'];
				}
			}
			if ( $this->mennyisegesOpcioszam < 0 ) {
				$this->mennyisegesOpcioszam = 100000000;
			}
		}
		$mysql->close;
	}
	
	public function getTulajdonsagok() {
		$mysql = connecti();
		$sql_seltulajdonsagok = "Select * From ".$this->prefix."termektulajdonsag Left Join ".$this->prefix."tulajdonsagertek on tulajdonsagertek_id = termektulajdonsag_ertek Left Join ".$this->prefix."tulajdonsagok on tulajdonsagok_id = tulajdonsagertek_tulajdonsag Where termektulajdonsag_termek = '$this->azon' and tulajdonsagok_aktiv = 1 and termektulajdonsag_id 
		IN ( Select termektulajdonsag_id From ".$this->prefix."termektulajdonsag Left Join ".$this->prefix."tulajdonsagertek on tulajdonsagertek_id = termektulajdonsag_ertek Where tulajdonsagertek_id is not null Group By tulajdonsagertek_tulajdonsag ) Order By termektulajdonsag_sorrend ASC";
		$ret_seltulajdonsagok = $mysql->query ($sql_seltulajdonsagok);
		//print $sql_seltulajdonsagok;
		//if ($ret_seltulajdonsagok->num_rows > 0) { print '<br />'; }
		$elozo = array();
		while ( $tul = $ret_seltulajdonsagok->fetch_array(MYSQLI_ASSOC) ) {
			$tulajdonsagid = $tul ['tulajdonsagok_id'];
			switch ($_COOKIE ['lang']) {
				case 'hu':
					$tulajdonsagnev = $tul ['tulajdonsagok_nev'];
					break;
				case 'en':
					$tulajdonsagnev = $tul ['tulajdonsagok_nev_en'];
					break;
				case 'de':
					$tulajdonsagnev = $tul ['tulajdonsagok_nev_de'];
					break;
				case 'fr':
					$tulajdonsagnev = $tul ['tulajdonsagok_nev_fr'];
					break;
				case 'ru':
					$tulajdonsagnev = $tul ['tulajdonsagok_nev_ru'];
					break;
				default:
					$tulajdonsagnev = $tul ['tulajdonsagok_nev'.$GLOBALS['site']->getLangDefKod()];
					break;
			}
			if ( !in_array($tulajdonsagnev, $elozo) ) {
				print '<strong>'.$tulajdonsagnev.':</strong> ';
				$sql_selertekek = "Select * From ".$this->prefix."termektulajdonsag Left Join ".$this->prefix."tulajdonsagertek on tulajdonsagertek_id = termektulajdonsag_ertek Where termektulajdonsag_termek = '$this->azon' and tulajdonsagertek_tulajdonsag = '$tulajdonsagid' Order By termektulajdonsag_sorrend ASC";
				$ret_selertekek = $mysql->query($sql_selertekek);
				$n = 0;
				while ( $er = $ret_selertekek->fetch_array(MYSQLI_ASSOC) ) {
					if ( $n == 0 ) {
						$n++;
					} else {
						print ', ';
					}
					print $er ['tulajdonsagertek_ertek'];
				}
				print '<br />';
				$elozo [] = $tulajdonsagnev;
			}
		}
		$mysql->close;
	}
	
	public function getOpciok() {
		$mysql = connecti();
		
		$sql_selvariaciok = "Select * From ".$this->prefix."termekopcio_mennyiseg Where termekopcio_mennyiseg_termek = '$this->azon' Order By termekopcio_mennyiseg_id ASC";
		$ret_selvariaciok = $mysql->query($sql_selvariaciok);
		while ( $v = $ret_selvariaciok->fetch_array(MYSQLI_ASSOC) ) {
			$opcarr = explode('[', $v ['termekopcio_mennyiseg_opciok']);
			$opcmenny = $v ['termekopcio_mennyiseg_keszlet'];
			$buttonparam = $v ['termekopcio_mennyiseg_opciok'];
			$buttonid = 'vb'.$v ['termekopcio_mennyiseg_id'];
			$buttontext = '';
			foreach ($opcarr as $key => $value) {
				$value = str_replace('[', '', $value);
				$value = str_replace(']', '', $value);
				$sql_selopciok = "Select * From ".$this->prefix."termekopcio Left Join ".$this->prefix."opcioertek on opcioertek_id = termekopcio_ertek Left Join ".$this->prefix."opciok on opciok_id = opcioertek_opcio Where termekopcio_termek = '$this->azon' and termekopcio_ertek = '$value' Group By opcioertek_opcio Order By termekopcio_sorrend ASC";
				$ret_selopciok = $mysql->query($sql_selopciok);
				while ( $so = $ret_selopciok->fetch_array(MYSQLI_ASSOC) ) {
					$buttontext .= $so ['opcioertek_ertek'].' ';
				}
				//
				//$buttontext = substr($buttontext, 0, -1);
			}
			$buttontext = trim($buttontext);
			if ( $opcmenny == 0 ) {
				$btdis = " disabled";
				$elfogy = $GLOBALS ['_webaruhaz_16'];
			} else {
				$btdis = "";
				$elfogy = '';
			}
			print '<div class="tooltip-box" style="display: inline-block;"><button'.$btdis.' onclick="variacioValaszt(\''.$buttonid.'\', \''.$buttonparam.'\');" id="'.$buttonid.'" type="button" class="btn btn-gomb btn-varia">'.$buttontext.'</button>';
				if ( $elfogy != '' ) {
					print '<span class="tooltip-text">'.$elfogy.'</span>';
				}
			print '</div>';
			//onclick action: Max mennyiség +--hoz, opció rejtett inputba érték.
		}

		print '<hr />';
		/*		
		$sql_selopciok = "Select * From ".$this->prefix."termekopcio Left Join ".$this->prefix."opcioertek on opcioertek_id = termekopcio_ertek Left Join ".$this->prefix."opciok on opciok_id = opcioertek_opcio Where termekopcio_termek = '$this->azon' Group By opcioertek_opcio Order By termekopcio_sorrend ASC";
		$ret_selopciok = $mysql->query ($sql_selopciok);
		while ($op = $ret_selopciok->fetch_array(MYSQLI_ASSOC)) {
			$opcioid = $op ['opciok_id'];
			switch ($_COOKIE ['lang']) {
				case 'hu':
					$opcionev = $op ['opciok_nev'];
					break;
				case 'en':
					$opcionev = $op ['opciok_nev_en'];
					break;
				case 'de':
					$opcionev = $op ['opciok_nev_de'];
					break;
				case 'fr':
					$opcionev = $op ['opciok_nev_fr'];
					break;
				case 'ru':
					$opcionev = $op ['opciok_nev_ru'];
					break;
				default:
					$opcionev = $op ['opciok_nev'.$GLOBALS['site']->getLangDefKod()];
					break;
			}
			print '<div class="valaszto-nagy form-group">
			<select name="topcio[]" id="topcio'.$op ['opciok_id'].'" class="form-control ktopcio chkopcio"><option value="">'.$opcionev.$GLOBALS ['_webaruhaz_168'].'</option>';
			$sql_selopertek = "Select * From ".$this->prefix."termekopcio Left Join ".$this->prefix."opcioertek on opcioertek_id = termekopcio_ertek Where termekopcio_termek = '$this->azon' and opcioertek_opcio = '$opcioid' Order By termekopcio_sorrend ASC";
			$ret_selopertek = $mysql->query($sql_selopertek);
			while ( $oe = $ret_selopertek->fetch_array(MYSQLI_ASSOC) ) {
				$oedis = '';
				$eotag = '';
				if ( $oe ['termekopcio_mennyiseg'] >= 0 ) {
					if ( $this->opcioMennyiseg == -1 ) {
						$this->opcioMennyiseg = 0;
					}
					$this->opcioMennyiseg += $oe ['termekopcio_mennyiseg'];
				}
				if ( $oe ['termekopcio_mennyiseg'] == 0 ) {
					$oedis = ' disabled';
					$oetag = ' - '.$GLOBALS ['_webaruhaz_184'];
				}
				print '<option'.$oedis.' value="'.$oe ['opcioertek_id'].'">'.$oe ['opcioertek_ertek'].$oetag.'</option>';
			}
			print '</select>
			</div>';
		}
		*/		
		$mysql->close;
	}

	public function createKosar() {
		$mysql = connecti();
		$dis = '';
		$kdis = '';
		if ( $this->aktiv == 1 and $this->mennyiseg != 0 ) {
			$dis = '';
		} else {
			$dis = ' disabled="true"';
		}

		if ( $this->opcioszam > 0) { $kdis = ' disabled="true"'; }

		if ( $dis != '' ) { $kdis = ''; }

		$uid = $_SESSION ['user_id'];
		if ( $uid == '' ) { $uid = session_id(); }

		$sql_iskedv = "Select * From ".$this->prefix."kedvenc Where kedvenc_user = '$uid' and kedvenc_termek = '".$this->azon."'";
   		//print $sql_iskedv;
		$ret_iskedv = $mysql->query ($sql_iskedv);
		$iskedv = 'glyphicon-heart-empty';
		if ( $ret_iskedv->num_rows == 1 ) {
			$iskedv = 'glyphicon-heart';
		} else {
			$iskedv = 'glyphicon-heart-empty';
		}

		print '
		<form class="kosarbox">
		<input type="hidden" name="cikkszam" id="cikkszam'.$this->azon.'" value="'.$this->azon.'" />
		<input type="hidden" name="ar" id="ar'.$this->azon.'" value="'.$this->getPlainAr().'" />
		<input type="hidden" name="variacio" id="variacio" value="" />
		';
		if ( $this->opcioszam > 0) {
			$this->getOpciok();
		}
		/*if ( $this->mennyiseg < 0 and $this->opcioMennyiseg != 0 ) {
			$maxmenny = 1000000000000;
		}
		if ( $this->mennyiseg > 0 ) {
			$maxmenny = $this->mennyiseg;
		}
		if ( $this->opcioMennyiseg == 0 ) {
			$maxmenny = 0;	
		}
		if ( $this->opcioMennyiseg > 0 ) {
			$maxmenny = $this->opcioMennyiseg;	
		}*/
		print '<div class="row">
		<div class="col-lg-3 col-md-3 col-sm-4 col-xs-12 mb-10">
		
		<div class="numberbox">
		<input type="button" name="decr" class="btn btn-left mnygomb" onclick="csokkent();" value="-"'.$dis.$kdis.'><input type="text" class="form-control inp" name="menny" id="menny" value="1"'.$dis.' readonly /><input type="button" name="incr" class="btn btn-right mnygomb" onclick="novel();" value="+"'.$dis.$kdis.' />
		</div>
		</div>
		<div class="col-lg-7 col-md-7 col-sm-4 col-xs-12 mb-10">
		<button class="btn btn-block btn-gomb ksrgomb"'.$dis.$kdis.' type="button" onclick="kosarba(\''.$this->azon.'\')" title="'.$GLOBALS['_webaruhaz_3'].'">'.$GLOBALS['_webaruhaz_3'].'</button>
		</div>
		<div class="col-lg-2 col-md-2 col-sm-4 col-xs-12 mb-10">
		<button class="btn btn-block btn-gomb-kedvenc" type="button" onclick="kedvencbe(\''.$this->azon.'\', \'kedv'.$this->azon.'\')" title="'.$GLOBALS['_webaruhaz_5'].'"><i class="glyphicon '.$iskedv.'" id="kedv'.$this->azon.'" aria-hidden="true"></i></button>
		</div></div>
		</form>';
		$mysql->close;
	}

	public function createSocials($url) {
		print '<a href="https://www.facebook.com/sharer/sharer.php?u='.$url.'" target="_blank" class="socialshare"><i class="fa fa-facebook-square tooltip-box" aria-hidden="true"><span class="tooltip-text">Facebook</span></i></a>
		<a href="https://twitter.com/intent/tweet?url='.$url.'" target="_blank" class="socialshare"><i class="fa fa-twitter-square tooltip-box" aria-hidden="true"><span class="tooltip-text">Twitter</span></i></a>
		<a href="https://pinterest.com/pin/create/bookmarklet/?&url='.$url.'" target="_blank" class="socialshare"><i class="fa fa-pinterest-square tooltip-box" aria-hidden="true"><span class="tooltip-text">Pinterest</span></i></a>';
	}

	public function getTermSpec() {
		if ( $this->szeles != '' and $this->szeles != '0' ) {
		$prmtext = ''.$GLOBALS['_webaruhaz_11'].' '.$this->szeles.' cm <br />';
		}
		if ( $this->hosszu != '' and $this->hosszu != '0' ) {
		$prmtext .= ''.$GLOBALS['_webaruhaz_12'].' '.$this->hosszu.' cm <br />';
		}
		if ( $this->magas != '' and $this->magas != '0' ) {
		$prmtext .= ''.$GLOBALS['_webaruhaz_13'].' '.$this->magas.' cm <br />';
		}
		return $prmtext;
	}

	public function getAr() {
		$bruttoki = 0;

		if ( $this->brutto != 0 ) {
			$bruttoki = $this->brutto;
		}

		if ( $this->akcbrutto != 0 ) {
			$bruttoki = $this->akcbrutto;
		}

		if ( $this->bombbrutto != 0 ) {
			$bruttoki = $this->bombbrutto;
		}

		if ( $this->aktiv == 1 ) {
			print '<span class="nagybrutto" id="nagybrutto">'.number_format($bruttoki, 0, ',', ' ').' Ft</span>';
		} else {
			print '<span class="nagybrutto" id="nagybrutto">'.$GLOBALS['_webaruhaz_14'].'</span>';
		}
	}

	public function getPlainAr() {
		$bruttoki = 0;

		if ( $this->brutto != 0 ) {
			$bruttoki = $this->brutto;
		}

		if ( $this->akcbrutto != 0 ) {
			$bruttoki = $this->akcbrutto;
		}

		if ( $this->bombbrutto != 0 ) {
			$bruttoki = $this->bombbrutto;
		}

		return $bruttoki;
	}

	public function getErtekeles() {
		$mysql = connecti();
		$uid = $_SESSION ['user_id'];
		$sql_selertekelesek = "Select * From ".$this->prefix."termek_ertekeles Where termek_ertekeles_termek = '$this->azon'";
		$ret_selertekelesek = $mysql->query($sql_selertekelesek);
		$ertekelesekSzama = $ret_selertekelesek->num_rows;
		$ertekelesekPontok = 0;
		$ertekeltem = false;
		$megrendeltem = false;
		while ( $ep = $ret_selertekelesek->fetch_array(MYSQLI_ASSOC) ) {
			$ertekelesekPontok += $ep ['termek_ertekeles_ertek'];
			if ( $ep ['termek_ertekeles_user'] == $uid ) {
				$ertekeltem = true;
			}
		}
		if ( $ertekelesekSzama > 0 ) {
			$ertekelesFull = $ertekelesekSzama*5;
			$ertekelesSzazalek = ($ertekelesekPontok/$ertekelesFull)*100;
		} else {
			$ertekelesSzazalek = 0;
		}

		if ( logged() ) {
			$sql_megrendeltem = "Select * From ".$this->prefix."rendeles Left Join ".$this->prefix."rendeles_tetel on rendtet_rend = rend_szam Where rend_user = '$uid' and rendtet_cikkszam = '$this->azon'";
			$ret_megrendeltem = $mysql->query($sql_megrendeltem);
			if ( $ret_megrendeltem->num_rows >= 1 ) {
				$megrendeltem = true;
			}
		}


  		//print 'Mennyiség: '.$ertekelesekSzama.', Százalék: '.$ertekelesSzazalek.'%';
		print '<span id="ertekeles">';
		if ( logged() and $megrendeltem ) {
			$pont = 1;
			for ($i = 0; $i < 100 ; $i += 20) {
				if ($i >= $ertekelesSzazalek) {
					if ( !$ertekeltem ) {
						print '<i id="star'.$pont.'" onclick="termekErtekeles(\''.$this->azon.'\', \''.$pont.'\');" class="ertekel fa fa-star-o" aria-hidden="true"></i>';

						//print '<i id="star'.$i.'" onclick="termekErtekeles(\''.$this->azon.'\', \''.$pont.'\');" onmouseenter="$(this).toggleClass(\'fa-star-o\');$(this).toggleClass(\'fa-star\');" onmouseleave="$(this).toggleClass(\'fa-star-o\');$(this).toggleClass(\'fa-star\');" class="ertekel fa fa-star-o" aria-hidden="true"></i>';
					} else {
						print '<i class="fa fa-star-o" aria-hidden="true"></i>';
					}
				} else {
					print '<i class="fa fa-star" aria-hidden="true"></i>';
				}
				$pont++;
			}
		} else {
			print $GLOBALS ['_webaruhaz_174'];
		}
		print '</span>';
		$mysql->close;
	}

	public function getUtvonal() {
		$mysql = connecti();
		$tkat = $this->$multiKategoria [0];
		$ref = $_SERVER['HTTP_REFERER'];
		if ( strstr($ref, 'webaruhaz') ) {
			$ktarr = explode('/', $ref);
			$key = array_search('webaruhaz', $ktarr);
			$nkey = $key+1;
			$katid = $ktarr [$nkey];
			if ( is_numeric($katid) ) {
            	$tkat = $katid;
        	} else {
            	$tkat = $this->$multiKategoria [0];    
        	}
		}
		$sql_selkat = "Select * From ".$this->prefix."kategoria Where kategoria_id = '$tkat' and kategoria_aktiv = 1";
		//print $sql_selkat;
		$ret_selkat = $mysql->query ($sql_selkat);
		while ( $sk = $ret_selkat->fetch_array(MYSQLI_ASSOC) ) {
			$katslugs = '';
			if ( $sk ['kategoria_main'] != 0 ) {
				getMainKat($sk ['kategoria_main']);
				$katslugs = getMainKatSlugs($sk ['kategoria_main']).'/'.$sk ['kategoria_slug'];
				print ' / ';
			} else {
				$katslugs = '/'.$sk ['kategoria_slug'];
			}
			$katslugs = str_replace('//', '/', $katslugs);
			print '<a href="'.$GLOBALS ['site_url'].'webaruhaz/'.$sk ['kategoria_id'].$katslugs.'">';
			switch ($_COOKIE ['lang']) {
				case 'hu':
				print $sk ['kategoria_nev'];
				break;
				case 'en':
				print $sk ['kategoria_nev_en'];
				break;
				case 'de':
				print $sk ['kategoria_nev_de'];
				break;
				case 'fr':
				print $sk ['kategoria_nev_fr'];
				break;
				case 'ru':
				print $sk ['kategoria_nev_ru'];
				break;
				default:
				print $sk ['kategoria_nev'.$GLOBALS['site']->getLangDefKod()];
				break;
			}
			print '</a><br />';
		}
		$mysql->close;
	}
	
	public function getUtvonal2() {
		$mysql = connecti();
		$tkat = $this->$multiKategoria [0];
		$ref = $_SERVER['HTTP_REFERER'];
		if ( strstr($ref, 'webaruhaz') ) {
			$ktarr = explode('/', $ref);
			$key = array_search('webaruhaz', $ktarr);
			$nkey = $key+1;
			$katid = $ktarr [$nkey];
			if ( is_numeric($katid) ) {
            	$tkat = $katid;
        	} else {
            	$tkat = $this->$multiKategoria [0];    
        	}
		}
		$sql_selkat = "Select * From ".$this->prefix."kategoria Where kategoria_id = '$tkat' and kategoria_aktiv = 1";
		$ret_selkat = $mysql->query ($sql_selkat);
		while ( $sk = $ret_selkat->fetch_array(MYSQLI_ASSOC) ) {
			$katslugs = '';
			if ( $sk ['kategoria_main'] != 0 ) {
				getMainKat2($sk ['kategoria_main']);
				$katslugs = getMainKatSlugs2($sk ['kategoria_main']).$sk ['kategoria_slug'];
				print ' / ';
			} else {
				$katslugs = '/'.$sk ['kategoria_slug'];
			}
			$katslugs = str_replace('//', '/', $katslugs);
			print '<a href="'.$GLOBALS ['site_url'].'webaruhaz/'.$sk ['kategoria_id'].$katslugs.'" class="fs-14">';
			switch ($_COOKIE ['lang']) {
				case 'hu':
				print $sk ['kategoria_nev'];
				break;
				case 'en':
				print $sk ['kategoria_nev_en'];
				break;
				case 'de':
				print $sk ['kategoria_nev_de'];
				break;
				case 'fr':
				print $sk ['kategoria_nev_fr'];
				break;
				case 'ru':
				print $sk ['kategoria_nev_ru'];
				break;
				default:
				print $sk ['kategoria_nev'.$GLOBALS['site']->getLangDefKod()];
				break;
			}
			print '</a>';
			
		}
		$mysql->close;
	}

	public function getKapcsTermsNum() {
		$mysql = connecti();
		$tkat = '%['.$this->$multiKategoria [0].']%';
		$kslugid = $this->$multiKategoria [0];
		$ref = $_SERVER['HTTP_REFERER'];
		$katfilter = '';
		/*
		foreach ($this->$multiKategoria as $key => $value) {
			if ( $value != '' ) {
				$vl = '%['.$value.']%';
				$katfilter .= " or termek_kategoria_id like '$vl'";
			}
		}
		*/
		if ( strstr($ref, 'webaruhaz') ) {
			$ktarr = explode('/', $ref);
			$key = array_search('webaruhaz', $ktarr);
			$nkey = $key+1;
			$katid = $ktarr [$nkey];
			$katfilter = "";
			if ( is_numeric($katid) ) {
            	$katfilter = '';
            	$tkat = '%['.$katid.']%';
            	$katids = array();
            	$sql_selkatid = "Select kategoria_id From ".$this->prefix."kategoria Where kategoria_id = '$katid' and kategoria_aktiv = 1";
            	$ret_selkatid = $mysql->query ($sql_selkatid);
            	while ( $ki = $ret_selkatid->fetch_array(MYSQLI_ASSOC) ) {
            		$katids = getSubkatListId($ki ['kategoria_id']);
            	}
            	$katarr = explode(',', $katids);
            	$katarr = array_unique($katarr);
            	foreach ($katarr as $key => $value) {
            		if ( $value != '' ) {
            			$vl = '%['.$value.']%';
            			$katfilter .= " or termek_kategoria_id like '$vl'";
            		}
            	}
        	} /*else {
            	$tkat = '%['.$this->$multiKategoria [0].']%';
        	}	*/		
		}
		
		$sql_selkpterm = "Select * From ".$this->prefix."termek Where (termek_kategoria_id like '$tkat'".$katfilter.") and termek_aktiv = 1 and termek_id != '$this->azon'";
		$ret_selkpterm = $mysql->query($sql_selkpterm);
		$mysql->close;
		return $ret_selkpterm->num_rows;
	}

	public function getKapcsTerms($lim) {
		$mysql = connecti();
		$tkat = '%['.$this->$multiKategoria [0].']%';
		$kslugid = $this->$multiKategoria [0];
		$ref = $_SERVER['HTTP_REFERER'];
		$katfilter = '';
		/*
		foreach ($this->$multiKategoria as $key => $value) {
			if ( $value != '' ) {
				$vl = '%['.$value.']%';
				$katfilter .= " or termek_kategoria_id like '$vl'";
			}
		}
		*/

		
		if ( strstr($ref, 'webaruhaz') ) {
			$ktarr = explode('/', $ref);
			$key = array_search('webaruhaz', $ktarr);
			$nkey = $key+1;
			$katid = $ktarr [$nkey];
			$katfilter = '';
			if ( is_numeric($katid) ) {
            	$katfilter = '';
            	$tkat = '%['.$katid.']%';
            	
            	$katids = array();
            	$sql_selkatid = "Select kategoria_id From ".$this->prefix."kategoria Where kategoria_id = '$katid' and kategoria_aktiv = 1";
            	$ret_selkatid = $mysql->query ($sql_selkatid);
            	while ( $ki = $ret_selkatid->fetch_array(MYSQLI_ASSOC) ) {
            		$katids = getSubkatListId($ki ['kategoria_id']);
            	}
            	$katarr = explode(',', $katids);
            	$katarr = array_unique($katarr);
            	foreach ($katarr as $key => $value) {
            		if ( $value != '' ) {
            			$vl = '%['.$value.']%';
            			$katfilter .= " or termek_kategoria_id like '$vl'";
            		}
            	}

        	} /*else {
            	$tkat = '%['.$this->$multiKategoria [0].']%';
            	$kslugid = $this->$multiKategoria [0];
            	//print 'NOTNUM';
        	}*/
		}
		

		$sql_selkpterm = "Select * From ".$this->prefix."termek Where (termek_kategoria_id like '$tkat'".$katfilter.") and termek_aktiv = 1 and termek_id != '$this->azon' Order By RAND() Limit ".$lim;
		//print $sql_selkpterm.'<br />';
		$ret_selkpterm = $mysql->query($sql_selkpterm);
		while ( $kpt = $ret_selkpterm->fetch_array(MYSQLI_ASSOC) ) {
			$katslugs = '';
    		$sql_seltkat = "Select * From ".$this->prefix."kategoria Where kategoria_id = '$kslugid'";
    		//print $sql_seltkat;
    		$ret_seltkat = $mysql->query($sql_seltkat);
    		while ( $tkt = $ret_seltkat->fetch_array(MYSQLI_ASSOC) ) {
        		if ( $tkt ['kategoria_main'] != 0 ) {
            		$katslugs = $tkt ['kategoria_katslug'];
        		} else {
            		$katslugs = '/'.$tkt ['kategoria_slug'];
        		}
    		}
			switch ($_COOKIE ['lang']) {
				case 'hu':
				$trmnev = $kpt ['termek_nev'];
				break;
				case 'en':
				$trmnev = $kpt ['termek_nev_en'];
				break;
				case 'de':
				$trmnev = $kpt ['termek_nev_de'];
				break;
				case 'fr':
				$trmnev = $kpt ['termek_nev_fr'];
				break;
				case 'ru':
				$trmnev = $kpt ['termek_nev_ru'];
				break;
				default:
				$trmnev = $kpt ['termek_nev'.$GLOBALS['site']->getLangDefKod()];
				break;
			}

			$dis = '';
			if ( $kpt ['termek_aktiv'] == 1 and $kpt ['termek_mennyiseg'] != 0 ) {
				$dis = '';
			} else {
				$dis = ' disabled="true"';
			}
			$sql_iskedv = "Select * From ".$this->prefix."kedvenc Where kedvenc_user = '$uid' and kedvenc_termek = '$ck'";
			//print $sql_iskedv;
			$ret_iskedv = $mysql->query ($sql_iskedv);
			$iskedv = 'glyphicon-heart-empty';
			if ( $ret_iskedv->num_rows == 1 ) {
				$iskedv = 'glyphicon-heart';
			} else {
				$iskedv = 'glyphicon-heart-empty';
			}
			$ck = $kpt ['termek_id'];
    		$sql_isopcios = "Select * From ".$this->prefix."termekopcio_mennyiseg Where termekopcio_mennyiseg_termek = '$ck' Limit 1";
    		$ret_isopcios = $mysql->query($sql_isopcios);
			print '<div class="col-md-12 kisterm-2 mt-20 mb-20">
    <div class="termek-kartya">
    <div class="kisimg-2">
    <a href="'.$GLOBALS ['site_url'].'termek/'.$kpt ['termek_id'].$katslugs.'/'.$kpt ['termek_slug'].'" class="d-b termekkep-racs">';
			$akcar = 0;
			if ( $kpt ['termek_akcbrutto'] != 0 ) {
				$akcar = $kpt ['termek_akcbrutto'];
			}
			if ( $akcar != 0 ) {
				//$akciomertek = 100-round(($akcar / $kpt ['termek_brutto'])*100);
				//print '<div class="akcflag">-'.$akciomertek.'%</div>';

				if ( $tr ['termek_akcio_mertek'] != 0 ) {
					$akciomertek = '-'.$kpt ['termek_akcio_mertek'].'%';
				} else {
					$akciomertek = $GLOBALS ['_webaruhaz_8'];
				}
				print '<div class="akcflag">'.$akciomertek.'</div>';
			}
			
			$keptermek = $kpt ['termek_id'];
			$sql_selkep = "Select * From ".$this->prefix."termek_kep Where termek_kep_termek = '$keptermek' Order by termek_kep_sorrend ASC Limit 1";
			$ret_selkep = $mysql->query($sql_selkep);
			if ( $ret_selkep->num_rows == 0 ) {
				print '<img src="'.$GLOBALS['site_url'].'content/site/no_image.png" title="'.htmlspecialchars(stripslashes($trmnev)).'" alt="'.htmlspecialchars(stripslashes($trmnev)).'" />';
			} else {
				while ( $tkp = $ret_selkep->fetch_array(MYSQLI_ASSOC) ) {
					print '<img src="'.$GLOBALS['site_url'].'content/shopimg/tn_'.$tkp ['termek_kep_kep'].'" title="'.htmlspecialchars(stripslashes($trmnev)).'" alt="'.htmlspecialchars(stripslashes($trmnev)).'" />';
				}
			}
			print '</a>
			<form class="kosarbox">
    <input type="hidden" name="cikkszam" id="cikkszam'.$kpt ['termek_id'].'" value="'.$kpt ['termek_id'].'" />
    <input type="hidden" name="szin" id="szin'.$kpt ['termek_id'].'" value="0" />
    <input type="hidden" name="ar" id="ar'.$kpt ['termek_id'].'" value="'.$ar.'" />
    ';    
    if ( $ret_isopcios->num_rows == 1 ) {
        print '<a href="'.$GLOBALS['site_url'].'termek/'.$kpt ['termek_id'].$katslugs.'/'.$kpt ['termek_slug'].'" class="btn_kosarba">'.$GLOBALS['_webaruhaz_183'].'</a>';
    } else {
    print '<button class="btn_kosarba"'.$dis.' type="button" onclick="kosarba(\''.$kpt ['termek_id'].'\')" title="'.$GLOBALS['_webaruhaz_3'].'">'.$GLOBALS['_webaruhaz_3'].'</button>';
	}
    print '<button class="btn_fav" type="button" onclick="kedvencbe(\''.$kpt ['termek_id'].'\', \'kedv'.$kpt ['termek_id'].'\')" title="'.$GLOBALS['_webaruhaz_5'].'"><i class="glyphicon '.$iskedv.'" id="kedv'.$kpt ['termek_id'].'" aria-hidden="true"></i></button>
    </form>
			</div>
			<h5 class="crop-text"><a href="'.$GLOBALS ['site_url'].'termek/'.$kpt ['termek_id'].$katslugs.'/'.$kpt ['termek_slug'].'" class="normal-link">'.htmlspecialchars(stripslashes($trmnev)).'</a></h5>

			<div class="termek-kartya-ar">';
			$oar = 0;
			if ( $kpt ['termek_akcbrutto'] == 0 ) {
				$ar = $kpt ['termek_brutto'];
			} else {
				$oar = $kpt ['termek_brutto'];
				$ar = $kpt ['termek_akcbrutto'];
			}
			print number_format($ar, 0, ',', ' ');
			print ' Ft';
			if ( $oar ) {
				print '<span class="athuzott pl-10">'.number_format($oar, 0, ',', ' ').' Ft</span>'; 
			}
			print '</div>';
			print '</div></div>';
		}
		$mysql->close;
	}

	public function getRaktInfo() {		
		if ( $this->mennyiseg != 0 and $this->mennyisegesOpcioszam != 0 ) {
			return '<div class="raktinfo green"><i class="fa fa-check" aria-hidden="true"></i> '.$GLOBALS['_webaruhaz_15'].'</div>';
		} else {
			return '<div class="raktinfo red"><i class="fa fa-times" aria-hidden="true"></i> '.$GLOBALS['_webaruhaz_16'].'</div>';
		}
	}

	public function getKep() {
		return $this->mainkep;
	}

	public function getKeplista() {
		$arr = $this->kisKeplista;
		$lst = '';
		foreach ($arr as $key=>$value) {
			$lst .= '<a href="content/shopimg/'.$value.'" class="nsbbox" alt="'.$this->title.'" title="'.$this->title.'"><img src="content/shopimg/tn_'.$value.'" alt="'.$this->title.'" title="'.$this->title.'" /></a>';
		}
		return $lst;
	}

	public function getSzallido() {
		if ( $this->aktiv == 1 ) {
			return $this->szallido;
		} else {
			return ''.$GLOBALS['_webaruhaz_14'].'';
		}
	}


	public function getKeyw() {
		switch ($_COOKIE ['lang']) {
			case 'hu':
			return $this->keyw;
			break;
			case 'en':
			return $this->keyw_en;
			break;
			case 'de':
			return $this->keyw_de;
			break;
			case 'fr':
			return $this->keyw_fr;
			break;
			case 'ru':
			return $this->keyw_ru;
			break;
			default:
			return $this->keyw_def;
			break;
		}
	}

	public function getDesc() {
		switch ($_COOKIE ['lang']) {
			case 'hu':
			return $this->desc;
			break;
			case 'en':
			return $this->desc_en;
			break;
			case 'de':
			return $this->desc_de;
			break;
			case 'fr':
			return $this->desc_fr;
			break;
			case 'ru':
			return $this->desc_ru;
			break;
			default:
			return $this->desc_def;
			break;
		}
	}

	public function getUtvonalTitleReverse() {
		$mysql = connecti();
		$tkat = $this->$multiKategoria [0];
		$ref = $_SERVER['HTTP_REFERER'];
		if ( strstr($ref, 'webaruhaz') ) {
			$ktarr = explode('/', $ref);
			$key = array_search('webaruhaz', $ktarr);
			$nkey = $key+1;
			$katid = $ktarr [$nkey];
			if ( is_numeric($katid) ) {
            	$tkat = $katid;
        	} else {
            	$tkat = $this->$multiKategoria [0];    
        	}
		}
		$rtv = '';
		$sql_selkat = "Select * From ".$this->prefix."kategoria Where kategoria_id = '$tkat' and kategoria_aktiv = 1";
		$ret_selkat = $mysql->query ($sql_selkat);
		while ( $sk = $ret_selkat->fetch_array(MYSQLI_ASSOC) ) {
			switch ($_COOKIE ['lang']) {
				case 'hu':
				$rtv = $sk ['kategoria_nev'];
				break;
				case 'en':
				$rtv = $sk ['kategoria_nev_en'];
				break;
				case 'de':
				$rtv = $sk ['kategoria_nev_de'];
				break;
				case 'fr':
				$rtv = $sk ['kategoria_nev_fr'];
				break;
				case 'ru':
				$rtv = $sk ['kategoria_nev_ru'];
				break;
				default:
				$rtv = $sk ['kategoria_nev'.$GLOBALS['site']->getLangDefKod()];
				break;
			}
			if ( $sk ['kategoria_main'] != 0 ) {
				$rtv .= getMainKat3($sk ['kategoria_main']);
			}
		}
		$mysql->close;
		return $rtv;
	}

	public function getTitle() {
		switch ($_COOKIE ['lang']) {
			case 'hu':
			return $this->title.' - '.$this->getUtvonalTitleReverse();
			break;
			case 'en':
			return $this->title_en.' - '.$this->getUtvonalTitleReverse();
			break;
			case 'de':
			return $this->title_de.' - '.$this->getUtvonalTitleReverse();
			break;
			case 'fr':
			return $this->title_fr.' - '.$this->getUtvonalTitleReverse();
			break;
			case 'ru':
			return $this->title_ru.' - '.$this->getUtvonalTitleReverse();
			break;
			default:
			return $this->title_def.' - '.$this->getUtvonalTitleReverse();
			break;
		}
	}

	public function getLeiras() {
		switch ($_COOKIE ['lang']) {
			case 'hu':
			return $this->termleir;
			break;
			case 'en':
			return $this->termleir_en;
			break;
			case 'de':
			return $this->termleir_de;
			break;
			case 'fr':
			return $this->termleir_fr;
			break;
			case 'ru':
			return $this->termleir_ru;
			break;
			default:
			return $this->termleir_def;
			break;
		}
	}
	public function getCikkszam() {
		return $this->cikkszam;
	}

	public function getLeirasRovid() {
		return mb_substr(strip_tags($this->termleir), 0, 250);
	}

	public function getAkcioBadge() {
		$akc_ar = 0;

		if ( $this->akcbrutto > 0 ) {
			$akc_ar = $this->akcbrutto;
		}

		if ( $akc_ar > 0 ) {
			if ($this->akcmertek != 0 ) {
				$akciomertek = '-'.$this->akcmertek.'%';
			} else {
				$akciomertek = $GLOBALS ['_webaruhaz_8'];
			}
			return '<div class="akcflag">'.$akciomertek.'</div><!-- akcflag -->';
		}
	 //return $this->akcbrutto;
	}

	public function getAkcioInfo() {
		if ( $this->akcbrutto > 0 or $this->bombbrutto > 0 ) {
			return ' '.$GLOBALS['_webaruhaz_18'].' ';
		}
	}

	public function getBrutto() {
		if ( $this->aktiv == 1 ) {
			return $this->brutto;
		} else {
			return ''.$GLOBALS['_webaruhaz_10'].'';
		}
	}

	public function genMicroData() {
print '<script type="application/ld+json">
 {
 "@context" : "http://schema.org/Product",
 "@type" : "Product",
 "author": "'.$GLOBALS['_head_meta_5'].'",
 "name" : "'.$this->getTitle().'",
 "image" : "'.getSiteURL().'content/shopimg/'.$this->getKep().'",
 "description" : "'.strip_tags($this->getLeiras()).', '.strip_tags($this->getTermSpec()).'",
 "url" : "'.getSiteURL().'termek/'.$this->slug.'", 
 "offers" : {
 "@type" : "Offer",
 "price" : "'.$this->getBrutto().'", 
 "priceCurrency" : "HUF"
 }
 }
</script>
';
	}
}

?>
