@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;900&family=Roboto:wght@300;400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@200,500;700&display=swap');

:root {
  --widthDiv: calc(100% - 1855px);
  --widthClc: calc(var(--widthDiv) / 2);
}

html, body {
 font-family: 'Roboto', sans-serif;
 margin: 0px;
 padding: 0px;
 font-size: 12px;
 text-align: left;
 color: #000000;
 background-color: #FFFFFF;
}

a:link, a:visited, a:active {
 -webkit-transition: color 500ms ease-in; /* Saf3.2+, Chrome */
 -moz-transition: color 500ms ease-in; /* FF3.7+ */
 -o-transition: color 500ms ease-in; /* Opera 10.5+ */
 transition: color 500ms ease-in; /* futureproofing */
}

a {
 color: #000000;
 text-decoration: underline;
}

.nopaddrow {
 padding: 0px;
 margin: 0px;
}

.container_full {
 width: 100%;
 padding: 0;
 margin: 0;
}

.container_site {
 margin-left: auto;
 margin-right: auto;
 width: 1330px;
 padding: 0;
}

.container_content {
 margin-left: auto;
 margin-right: auto;
 width: 1194px;
 padding: 0;
}

.header {
  width: 100%;
  height: 450px;
  background-image: url('../img/header_bg.jpg');
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  background-size: cover;
  margin-top: -20px;
}

.header .mnu {
  width: 1114px;
  float: left;
}

.header .felso_nav {
  height: 20px;
  width: 1114px;
  font-family: 'Roboto', sans-serif;
  color: #FFFFFF;
}

.header .felso_nav .felso_nav_bal {
  float: left;
  padding-left: 20px;
}

.header .felso_nav .felso_nav_bal a {
  padding-left: 10px;
  padding-right: 10px;
  font-size: 1.1em;
}

.header .felso_nav .felso_nav_jobb {
  float: right;
}

.header .felso_nav .felso_nav_jobb a {
  padding-left: 10px;
  padding-right: 10px;
  font-size: 1.2em;
}

.header h1 {
  font-family: 'Roboto', sans-serif;
  color: #FFFFFF;
  padding-top: 150px;
  font-size: 6em;
  font-weight: normal;
  font-variant: small-caps
}

.idopont {
  margin-top: -100px;
  position: relative;
  z-index: 2500;
  background-color: white;
  font-family: 'Roboto', sans-serif;
  padding: 20px;
}

.idopont h2 {
  font-variant: small-caps;
  font-size: 3em;
}

.margin-30 {
  margin-top: 30px;
}

.idopont .idopontform .control-label {
  margin-left: 10px;
  background-color: white;
  position: relative;
  top: 15px;
  font-weight: normal;
  padding-left: 5px;
  padding-right: 5px;
  position: relative;
  font-size: 1.1em;
}

.idopont .idopontform .form-group {
  /*margin-top: 10px;*/
}

.idopont .idopontform .form-control {
  padding: 20px;
  border: 1px solid #000000;
  color: #000000;
}

.idopont .idopontform .form-control2 {
  line-height: 20px;
  padding: 0px;
  height: 42px;
  padding-left: 20px;
}

.header .menu {
  height: 100px;
  width: 100%;
  z-index: 1000;
  position: absolute;
  top: 25px;
  font-family: 'Roboto', sans-serif;
  color: #FFFFFF;
}

.header .menu .logo {
  width: 80px;
  height: 72px;
  float: left;
}

.header .menu .navigacio {
  width: 1114;
  height: 32px;
  float: left;
  line-height: 32px;
  font-size: 1.3em;
  font-weight: 500;
}

.menu a {
  color: #FFFFFF;
  text-decoration: none;
}

.menu img {
  max-height: 100px;
}

/* NAV mod */
.navbar-nav > li > a {
  line-height: 32px;
  font-size: 1.2em;
  text-transform: uppercase;
  font-weight: 200;
}

.navbar-nav > li > a:hover {
  background-color: transparent;
}

.navbar-nav .menuwhite {
  background-color: #FFFFFF;
  -webkit-border-radius: 26px;
  -moz-border-radius: 26px;
  border-radius: 26px;
  padding: 20px;
  height: auto;
  color: #000000;
}
/* NAV mod */

/*SLIDER*/

.slider {
 width: 100%;
 height: 100%;
 z-index: 1;
 position: absolute;
 top: 150px;
 overflow: hidden;
 text-align: right;
}

.slider .sld {

}

.slider .sld img {
  margin-top: auto;
  margin-bottom: auto;
  margin-right: 100px;
}

.slider .slcont{
 width: 100%;
 text-align: left;
 color: #FFFFFF;
 position: absolute;
 top: 257px;
 margin-left: 250px;
 /*text-px;nsform: uppercase;*/
}

.slcont .slgrey {
 font-size: 4.0em;
 font-weight: 700;
 font-family: 'Montserrat', sans-serif;
 color: #000000;
 /*text-shadow: 0px 0px 14px rgba(255, 255, 255, 0.4);*/
}

.slcont .slbig {
 font-size: 3.0em;
 font-family: 'Montserrat', sans-serif;
 font-weight: bold;
 color: #add138;
}

.slcont p {
 font-size: 1.4em;
 font-family: 'Montserrat', sans-serif;
 /*font-weight: bold;*/
 color: #000000;
 width: 500px;
 margin-top: 50px;
}

.slcont .input-group {
 background-color: rgba(255,255,255,0.5);
 border: 1px solid #FFFFFF;
 width: 600px;
 margin-top: 35px;
 font-size: 1.5em;
}

.slcont input {
 background-color: transparent;
 border: 0px;
 color: #000000;
 height: 50px;
}

.slcont .btn-link {
 color: #FFFFFF;

 background: rgba(209,200,77,1);
 background: -moz-linear-gradient(-75deg, rgba(209,200,77,1) 0%, rgba(173,209,56,1) 100%);
 background: -webkit-gradient(left top, right bottom, color-stop(0%, rgba(209,200,77,1)), color-stop(100%, rgba(173,209,56,1)));
 background: -webkit-linear-gradient(-75deg, rgba(209,200,77,1) 0%, rgba(173,209,56,1) 100%);
 background: -o-linear-gradient(-75deg, rgba(209,200,77,1) 0%, rgba(173,209,56,1) 100%);
 background: -ms-linear-gradient(-75deg, rgba(209,200,77,1) 0%, rgba(173,209,56,1) 100%);
 background: linear-gradient(-75deg, rgba(209,200,77,1) 0%, rgba(173,209,56,1) 100%);
 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d1c84d', endColorstr='#add138', GradientType=1 );

 -webkit-box-shadow: 0px 13px 35px 0px rgba(0,0,0,0.2);
 -moz-box-shadow: 0px 13px 35px 0px rgba(0,0,0,0.2);
 box-shadow: 0px 13px 35px 0px rgba(0,0,0,0.2);

 border: 0px;
 width: 155px;
 height: 46px;
 -webkit-border-radius: 23px;
 -moz-border-radius: 23px;
 border-radius: 23px;
 text-decoration: none;
 line-height: 35px;
 font-weight: bold;
 font-size: 1.3em;
 font-family: 'Roboto', sans-serif;
 margin-top: 55px;
 opacity: 0.75;
}

/*SLIDER*/

.szolg h2 {
  font-variant: small-caps;
  font-size: 3em;
  margin-bottom: 50px;
}

.hirbox {
  margin-bottom: 40px;
  /*border-bottom: 1px solid #e2e2e2;*/
  min-height: 250px;
}

.hirbox .hbkep {
  width: 100%;
  height: 100%;
  min-height: 250px;
  background-size: cover;
  background-repeat: no-repeat;
  overflow: hidden;
}

.hirbox .hbkep h3 {
  width: 100%;
  text-align: center;
  margin-top: 150px;
  background-color: rgba(0, 0, 0, 0.6);
  padding-top: 15px;
  padding-bottom: 15px;
  transition: all .5s ease;
}

.hirbox .hbkep:hover h3 {
  width: 100%;
  text-align: center;
  margin-top: 70px;
  background-color: rgba(0, 0, 0, 0.6);
  padding-top: 15px;
  padding-bottom: 15px;
  transition: all .5s ease;
}

.hirbox a {
  text-decoration: none;
  color: white;
  transition: all .5s ease;
}

.hirbox a:hover {
  text-decoration: underline;
  color: white;
  transition: all .5s ease;
}

.bemut {
  position: relative;
  margin-top: 50px;
}

.alja {
  background-image: url('../img/alja_bg.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  min-height: 500px;
}

.altxt {
  z-index: 1000;
  margin-left: -200px;
  margin-top: 100px;
  background-color: #FFFFFF;
  padding: 50px;
  font-size: 1.2em;
  font-family: Roboto, sans-serif;
  box-shadow: 0px 0px 7px 8px rgba(0,0,0,0.1);
-webkit-box-shadow: 0px 0px 7px 8px rgba(0,0,0,0.1);
-moz-box-shadow: 0px 0px 7px 8px rgba(0,0,0,0.05);
}

.footer {
  background-color: rgba(0,0,0,0.9);
  margin-top: 50px;
  padding-top: 40px;
  padding-bottom: 40px;
}

.footer .whiteblock {
 width: 100%;
 height: 269px;
 color: #FFFFFF;
 font-family: 'Roboto';
}

.whiteblock .rnd {
 margin-left: 31px;
 width: 335px;
 height: 228px;
 overflow: hidden;
 float: left;
}

.whiteblock h4 {
 color: #FFFFFF;
 font-family: 'Roboto';
 font-size: 1.8em;
 font-weight: 500;
 text-transform: uppercase;
}

.rnd .rline {
 height: 58px;
 line-height: 58px;
 font-size: 2.0em;
 color: #FFFFFF;
 margin-bottom: 23px;
 font-weight: bold;
}

.rline img {
 float: left;
 margin-right: 20px;
}

.whiteblock .rol {
 margin-left: 31px;
 width: 400px;
 height: 228px;
 overflow: hidden;
 float: left;
 text-align: justify;
}

.rol div {
 line-height: 25px;
 padding-right: 35px;
}

.whiteblock .alsomenu {
 float: left;
 width: 150px;
}

.alsomenu ul {
 color: #FFFFFF;
 list-style-position: outside;
 margin-left: 0;
 margin-top: 10px;
 padding-left: 0;
 width: 140px;
}

.alsomenu ul a {
 line-height: 25px;
 text-transform: none;
 color: #FFFFFF;
 padding-top: 10px;
 padding-bottom: 10px;
}

.uzl {
 float: left;
 width: 200px;
}

.uzl div {
 line-height: 25px;
 padding-right: 35px;
}

.footer .lastfoot {
 width: 100%;
 margin-bottom: 70px;
 position: relative;
 color: #FFFFFF;
 margin-top: 10px;
}

.lastfoot a {
 color: #ffcb05;
}

.lastfoot .fblink {
 position: absolute;
 top: -10px;
 right: 65px;
}

.lastfoot .fellink {
 position: absolute;
 top: -10px;
 right: 0px;
}

.termleir {
  font-size: 1.2em;
}

.adatkep {
  float: left;
  margin-right: 20px;
  margin-bottom: 20px;
  width: 30vw;
}

.adatkep img {
  width: 100%;
}

#suti {
 background-color: #000000;
 position: fixed;
 bottom: 0px;
 left: 0px;
 width: 100%;
 height: 60px;
 z-index: 1000000; 
 color: #FFFFFF;

}

#suti a {
 color: #FFFFFF;
 text-decoration: underline;
}

#suti .sutigomb {
 background-color: #FFCB05;
 color: #000000;
 display: block;
 height: 50px;
 line-height: 50px;
 text-align: center;
 margin-top: 5px;
 float: right;
 margin-right: 10px;
 padding-left: 15px;
 padding-right: 15px;
}

@media all and (max-width: 430px) {

  .container_site {
    width: 100%;
  }

  .container_content {
    width: 100%;
  }

  .header .mnu {
    width: 100%;
  }

  .header .felso_nav {
    width: 100%;
  }

  .header {
      background-position: 23% top;
  }

  .header h1 {
    font-size: 4.7em;
  }

  .header .menu {
    height: auto;
    top: 20px;
  }

  .header .menu .logo {
    float: left;
    text-align: center;
    margin-left: 5%;
  }

  .header .menu .logo img {
    width: 100%;
  }

  .header .menu .navigacio {
    height: auto;
    width: 30%;
    min-width: 0;
    max-width: none;
    float: right;
    line-height: normal;
    font-size: 1.3em;
    font-weight: 500;
    position: absolute;
    top: 10px;
    right: 0px;
  }
  
  .menu .navbar {
    border: 0px;
    width: 100%;
    position: relative;
  }

  .navbar #navbar {
    border: 0px;
    background: rgba(255,255,255,1);
    color: #000000;
    
  }

  .navbar #navbar li a {
    line-height: 20px;
    font-size: 1em;
    color: #000000;
  }

  .navbar-collapse {
    max-height: 100%;
    margin-left: -100px;
  }

  .menu .navbar-header {
    float: right;
  }

  .container_site {
    width: 100%;
  }

  .icon-bar {
    /*background-color: #FFFFFF;*/
    width: 50px !important;
    height: 3px !important;
    background-color: #FFFFFF;
  }

  .whiteblock .rol {
    width: 90%;
  }

  .altxt {
    z-index: 1000;
    margin-left: 0;
  }

  .whiteblock .alsomenu {
    width: 90%;
    margin-left: 31px;
  }

  .uzl {
    width: 90%;
    margin-left: 31px;
  }

  .adatlap, .utvonal  {
    width: 90%;
    padding: 5%;
  }

}

@media all and (min-width: 435px) and (max-width: 1100px) {

  .slider {
    width: 100%;
    height: 100%;
    z-index: 1;
    position: absolute;
    top: 100px;
    overflow: hidden;
    text-align: right;
  }

  .slider .sld {
    height: 100%;
  }

  .slider .sld img {
    /*
    margin-top: auto;
    margin-bottom: auto;
    */
    margin-right: 0px;
    width: 50vh;
  }

  .slider .slcont{
    width: 90vw;
    top: 0px;
    margin-left: 5vw;
    /*text-px;nsform: uppercase;*/
  }

  .slcont .slgrey {
    font-size: 3.5em;
  }

  .slcont .slbig {
    font-size: 2.5em;
  }

  .slcont p {
    width: 60%;
    margin-top: 20px;
  }

  .header {
      background-position: 13% top;
      min-height: 480px;
      height: auto;
  }

  .header .gradient {
    display: none;
  }

  .header .menu {
    height: auto;
    top: 0px;
  }

  .header .menu .logo {
    width: 20%;
    height: 92px;
    float: left;
    text-align: left;
    margin-left: 5%;
    margin-top: 10px;
  }

  .header .menu .logo img {
    width: 100%;
  }

  .header .menu .navigacio {
    height: auto;
    width: 70%;
    min-width: 0;
    max-width: none;
    float: right;
    line-height: normal;
    font-size: 1.3em;
    font-weight: 500;
  }
  
  .menu .navbar {
    border: 0px;
  }

  .navbar #navbar {
    border: 0px;
    background: rgba(69,169,121,1);
    background: -moz-linear-gradient(top, rgba(69,169,121,1) 0%, rgba(197,197,81,1) 100%);
    background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(69,169,121,1)), color-stop(100%, rgba(197,197,81,1)));
    background: -webkit-linear-gradient(top, rgba(69,169,121,1) 0%, rgba(197,197,81,1) 100%);
    background: -o-linear-gradient(top, rgba(69,169,121,1) 0%, rgba(197,197,81,1) 100%);
    background: -ms-linear-gradient(top, rgba(69,169,121,1) 0%, rgba(197,197,81,1) 100%);
    background: linear-gradient(to bottom, rgba(69,169,121,1) 0%, rgba(197,197,81,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#45a979', endColorstr='#c5c551', GradientType=0 );
    text-align: center;
    -webkit-box-shadow: -18px 18px 0px 0px rgba(212,211,174,0.5);
    -moz-box-shadow: -18px 18px 0px 0px rgba(212,211,174,0.5);
    box-shadow: -18px 18px 0px 0px rgba(212,211,174,0.5);
  }

  .navbar #navbar li a {
    line-height: 20px;
    font-size: 0.9em;
  }

  .navbar #navbar li a img {
    width: 40%;
  }

  .navbar-collapse {
    max-height: 100%;
  }

  .menu .navbar-header {
    float: right;
  }

  .container_site {
    width: 100%;
  }

  .icon-bar {
    /*background-color: #FFFFFF;*/
    width: 50px !important;
    height: 3px !important;
    background-color: #FFFFFF;
  }

  .whiteblock .rol {
    width: 90%;
  }

}